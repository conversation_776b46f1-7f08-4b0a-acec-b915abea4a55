{"name": "backend", "lockfileVersion": 3, "requires": true, "packages": {"": {"dependencies": {"@emailjs/browser": "^4.4.1", "acme-client": "^5.4.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-session": "^1.18.1", "jose": "^6.0.11", "latest": "^0.2.0", "mailtrap": "^4.1.0", "mysql2": "^3.14.1", "node-cron": "^3.0.3", "node-device-detector": "^2.2.1", "nodemailer": "^6.9.4", "passport": "^0.7.0", "passport-local": "^1.0.0", "request-ip": "^3.3.0", "zerossl-client": "^1.1.1"}}, "node_modules/@emailjs/browser": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/@emailjs/browser/-/browser-4.4.1.tgz", "integrity": "sha512-DGSlP9sPvyFba3to2A50kDtZ+pXVp/0rhmqs2LmbMS3I5J8FSOgLwzY2Xb4qfKlOVHh29EAutLYwe5yuEZmEFg==", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=14.0.0"}}, "node_modules/@peculiar/asn1-cms": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-cms/-/asn1-cms-2.3.15.tgz", "integrity": "sha512-B+DoudF+TCrxoJSTjjcY8Mmu+lbv8e7pXGWrhNp2/EGJp9EEcpzjBCar7puU57sGifyzaRVM03oD5L7t7PghQg==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "@peculiar/asn1-x509-attr": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-csr": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-csr/-/asn1-csr-2.3.15.tgz", "integrity": "sha512-caxAOrvw2hUZpxzhz8Kp8iBYKsHbGXZPl2KYRMIPvAfFateRebS3136+orUpcVwHRmpXWX2kzpb6COlIrqCumA==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-ecc": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-ecc/-/asn1-ecc-2.3.15.tgz", "integrity": "sha512-/HtR91dvgog7z/WhCVdxZJ/jitJuIu8iTqiyWVgRE9Ac5imt2sT/E4obqIVGKQw7PIy+X6i8lVBoT6wC73XUgA==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-pfx": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-pfx/-/asn1-pfx-2.3.15.tgz", "integrity": "sha512-E3kzQe3J2xV9DP6SJS4X6/N1e4cYa2xOAK46VtvpaRk8jlheNri8v0rBezKFVPB1rz/jW8npO+u1xOvpATFMWg==", "license": "MIT", "dependencies": {"@peculiar/asn1-cms": "^2.3.15", "@peculiar/asn1-pkcs8": "^2.3.15", "@peculiar/asn1-rsa": "^2.3.15", "@peculiar/asn1-schema": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-pkcs8": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-pkcs8/-/asn1-pkcs8-2.3.15.tgz", "integrity": "sha512-/PuQj2BIAw1/v76DV1LUOA6YOqh/UvptKLJHtec/DQwruXOCFlUo7k6llegn8N5BTeZTWMwz5EXruBw0Q10TMg==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-pkcs9": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-pkcs9/-/asn1-pkcs9-2.3.15.tgz", "integrity": "sha512-yiZo/1EGvU1KiQUrbcnaPGWc0C7ElMMskWn7+kHsCFm+/9fU0+V1D/3a5oG0Jpy96iaXggQpA9tzdhnYDgjyFg==", "license": "MIT", "dependencies": {"@peculiar/asn1-cms": "^2.3.15", "@peculiar/asn1-pfx": "^2.3.15", "@peculiar/asn1-pkcs8": "^2.3.15", "@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "@peculiar/asn1-x509-attr": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-rsa": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-rsa/-/asn1-rsa-2.3.15.tgz", "integrity": "sha512-p6hsanvPhexRtYSOHihLvUUgrJ8y0FtOM97N5UEpC+VifFYyZa0iZ5cXjTkZoDwxJ/TTJ1IJo3HVTB2JJTpXvg==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-schema": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-schema/-/asn1-schema-2.3.15.tgz", "integrity": "sha512-QPeD8UA8axQREpgR5UTAfu2mqQmm97oUqahDtNdBcfj3qAnoXzFdQW+aNf/tD2WVXF8Fhmftxoj0eMIT++gX2w==", "license": "MIT", "dependencies": {"asn1js": "^3.0.5", "pvtsutils": "^1.3.6", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-x509": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-x509/-/asn1-x509-2.3.15.tgz", "integrity": "sha512-0dK5xqTqSLaxv1FHXIcd4Q/BZNuopg+u1l23hT9rOmQ1g4dNtw0g/RnEi+TboB0gOwGtrWn269v27cMgchFIIg==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.15", "asn1js": "^3.0.5", "pvtsutils": "^1.3.6", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-x509-attr": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-x509-attr/-/asn1-x509-attr-2.3.15.tgz", "integrity": "sha512-TWJVJhqc+IS4MTEML3l6W1b0sMowVqdsnI4dnojg96LvTuP8dga9f76fjP07MUuss60uSyT2ckoti/2qHXA10A==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/x509": {"version": "1.12.3", "resolved": "https://registry.npmjs.org/@peculiar/x509/-/x509-1.12.3.tgz", "integrity": "sha512-+Mzq+W7cNEKfkNZzyLl6A6ffqc3r21HGZUezgfKxpZrkORfOqgRXnS80Zu0IV6a9Ue9QBJeKD7kN0iWfc3bhRQ==", "license": "MIT", "dependencies": {"@peculiar/asn1-cms": "^2.3.13", "@peculiar/asn1-csr": "^2.3.13", "@peculiar/asn1-ecc": "^2.3.14", "@peculiar/asn1-pkcs9": "^2.3.13", "@peculiar/asn1-rsa": "^2.3.13", "@peculiar/asn1-schema": "^2.3.13", "@peculiar/asn1-x509": "^2.3.13", "pvtsutils": "^1.3.5", "reflect-metadata": "^0.2.2", "tslib": "^2.7.0", "tsyringe": "^4.8.0"}}, "node_modules/accepts": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/accepts/-/accepts-2.0.0.tgz", "integrity": "sha512-5cvg6CtKwfgdmVqY1WIiXKc3Q1bkRqGLi+2W/6ao+6Y7gu/RCwRuAhGEzh5B4KlszSuTLgZYuqFqo5bImjNKng==", "dependencies": {"mime-types": "^3.0.0", "negotiator": "^1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/acme-client": {"version": "5.4.0", "resolved": "https://registry.npmjs.org/acme-client/-/acme-client-5.4.0.tgz", "integrity": "sha512-mORqg60S8iML6XSmVjqjGHJkINrCGLMj2QvDmFzI9vIlv1RGlyjmw3nrzaINJjkNsYXC41XhhD5pfy7CtuGcbA==", "license": "MIT", "dependencies": {"@peculiar/x509": "^1.11.0", "asn1js": "^3.0.5", "axios": "^1.7.2", "debug": "^4.3.5", "node-forge": "^1.3.1"}, "engines": {"node": ">= 16"}}, "node_modules/asn1js": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/asn1js/-/asn1js-3.0.6.tgz", "integrity": "sha512-UOCGPYbl0tv8+006qks/dTgV9ajs97X2p0FAbyS2iyCRrmLSRolDaHdp+v/CLgnzHc3fVB+CwYiUmei7ndFcgA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"pvtsutils": "^1.3.6", "pvutils": "^1.1.3", "tslib": "^2.8.1"}, "engines": {"node": ">=12.0.0"}}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==", "license": "MIT"}, "node_modules/aws-ssl-profiles": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/aws-ssl-profiles/-/aws-ssl-profiles-1.1.2.tgz", "integrity": "sha512-NZKeq9AfyQvEeNlN0zSYAaWrmBffJh3IELMZfRpJVWgrpEbtEpnjvzqBPf+mxoI287JohRDoa+/nsfqqiZmF6g==", "engines": {"node": ">= 6.0.0"}}, "node_modules/axios": {"version": "1.9.0", "resolved": "https://registry.npmjs.org/axios/-/axios-1.9.0.tgz", "integrity": "sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg==", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/bcryptjs": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-3.0.2.tgz", "integrity": "sha512-k38b3XOZKv60C4E2hVsXTolJWfkGRMbILBIe2IBITXciy5bOsTKot5kDrf3ZfufQtQOUN5mXceUEpU1rTl9Uog==", "bin": {"bcrypt": "bin/bcrypt"}}, "node_modules/body-parser": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-2.2.0.tgz", "integrity": "sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg==", "dependencies": {"bytes": "^3.1.2", "content-type": "^1.0.5", "debug": "^4.4.0", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "on-finished": "^2.4.1", "qs": "^6.14.0", "raw-body": "^3.0.0", "type-is": "^2.0.0"}, "engines": {"node": ">=18"}}, "node_modules/bytes": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "integrity": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==", "engines": {"node": ">= 0.8"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/content-disposition": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-1.0.0.tgz", "integrity": "sha512-Au9nRL8VNUut/XSzbQA38+M78dzP4D+eqg3gfJHMIHHYa3bg067xj1KxMUWj+VULbiZMowKngFFbKczUrNJ1mg==", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "integrity": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.7.2", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.7.2.tgz", "integrity": "sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w==", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.2.2.tgz", "integrity": "sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==", "engines": {"node": ">=6.6.0"}}, "node_modules/cors": {"version": "2.8.5", "resolved": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz", "integrity": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/debug": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz", "integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/denque": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/denque/-/denque-2.1.0.tgz", "integrity": "sha512-HVQE3AAb/pxF8fQAoiqpvg9i3evqug3hoiwakOyZAwJm+6vZehbkYXZ0l4JxS+I3QxM97v5aaRNhj8v5oBhekw==", "engines": {"node": ">=0.10"}}, "node_modules/depd": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==", "engines": {"node": ">= 0.8"}}, "node_modules/dotenv": {"version": "16.5.0", "resolved": "https://registry.npmjs.org/dotenv/-/dotenv-16.5.0.tgz", "integrity": "sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg==", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow=="}, "node_modules/encodeurl": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz", "integrity": "sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==", "engines": {"node": ">= 0.8"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "integrity": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow=="}, "node_modules/etag": {"version": "1.8.1", "resolved": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "integrity": "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==", "engines": {"node": ">= 0.6"}}, "node_modules/express": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/express/-/express-5.1.0.tgz", "integrity": "sha512-DT9ck5YIRU+8GYzzU5kT3eHGA5iL+1Zd0EutOmTE9Dtk+Tvuzd23VBU+ec7HPNSTxXYO55gPV/hq4pSBJDjFpA==", "dependencies": {"accepts": "^2.0.0", "body-parser": "^2.2.0", "content-disposition": "^1.0.0", "content-type": "^1.0.5", "cookie": "^0.7.1", "cookie-signature": "^1.2.1", "debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "finalhandler": "^2.1.0", "fresh": "^2.0.0", "http-errors": "^2.0.0", "merge-descriptors": "^2.0.0", "mime-types": "^3.0.0", "on-finished": "^2.4.1", "once": "^1.4.0", "parseurl": "^1.3.3", "proxy-addr": "^2.0.7", "qs": "^6.14.0", "range-parser": "^1.2.1", "router": "^2.2.0", "send": "^1.1.0", "serve-static": "^2.2.0", "statuses": "^2.0.1", "type-is": "^2.0.1", "vary": "^1.1.2"}, "engines": {"node": ">= 18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/express-session": {"version": "1.18.1", "resolved": "https://registry.npmjs.org/express-session/-/express-session-1.18.1.tgz", "integrity": "sha512-a5mtTqEaZvBCL9A9aqkrtfz+3SMDhOVUnjafjo+s7A9Txkq+SVX2DLvSp1Zrv4uCXa3lMSK3viWnh9Gg07PBUA==", "dependencies": {"cookie": "0.7.2", "cookie-signature": "1.0.7", "debug": "2.6.9", "depd": "~2.0.0", "on-headers": "~1.0.2", "parseurl": "~1.3.3", "safe-buffer": "5.2.1", "uid-safe": "~2.1.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/express-session/node_modules/cookie-signature": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.7.tgz", "integrity": "sha512-NXdYc3dLr47pBkpUCHtKSwIOQXLVn8dZEuywboCOJY/osA0wFSLlSawr3KN8qXJEyX66FcONTH8EIlVuK0yyFA=="}, "node_modules/express-session/node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dependencies": {"ms": "2.0.0"}}, "node_modules/express-session/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}, "node_modules/finalhandler": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-2.1.0.tgz", "integrity": "sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q==", "dependencies": {"debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "statuses": "^2.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/follow-redirects": {"version": "1.15.9", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz", "integrity": "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/form-data": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.2.tgz", "integrity": "sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/form-data/node_modules/mime-db": {"version": "1.52.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/form-data/node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/fresh/-/fresh-2.0.0.tgz", "integrity": "sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A==", "engines": {"node": ">= 0.8"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/generate-function": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/generate-function/-/generate-function-2.3.1.tgz", "integrity": "sha512-eeB5GfMNeevm/GRYq20ShmsaGcmI81kIX2K9XQx5miC8KdHaC6Jm0qQ8ZNeGOi7wYB8OsdxKs+Y2oVuTFuVwKQ==", "dependencies": {"is-property": "^1.0.2"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==", "engines": {"node": ">= 0.10"}}, "node_modules/is-promise": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/is-promise/-/is-promise-4.0.0.tgz", "integrity": "sha512-hvpoI6korhJMnej285dSg6nu1+e6uxs7zG3BYAm5byqDsgJNWwxzM6z6iZiAgQR4TJ30JmBTOwqZUw3WlyH3AQ=="}, "node_modules/is-property": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-property/-/is-property-1.0.2.tgz", "integrity": "sha512-Ks/IoX00TtClbGQr4TWXemAnktAQvYB7HzcCxDGqEZU6oCmb2INHuOoKxbtR+HFkmYWBKv/dOZtGRiAjDhj92g=="}, "node_modules/jose": {"version": "6.0.11", "resolved": "https://registry.npmjs.org/jose/-/jose-6.0.11.tgz", "integrity": "sha512-QxG7EaliDARm1O1S8BGakqncGT9s25bKL1WSf6/oa17Tkqwi8D2ZNglqCF+DsYF88/rV66Q/Q2mFAy697E1DUg==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/panva"}}, "node_modules/latest": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/latest/-/latest-0.2.0.tgz", "integrity": "sha512-nsIM/FjwLcsKZ1KDAw5CivnM26zzMs3zGBL4SdjYXHI5tMcOWjGhFDMBKIum4WNAkZmeVw7zU1jR2H2UiKoQVA==", "dependencies": {"npm": "^2.5.1"}, "bin": {"latest": "bin/latest.js"}, "engines": {"node": "*"}}, "node_modules/long": {"version": "5.3.2", "resolved": "https://registry.npmjs.org/long/-/long-5.3.2.tgz", "integrity": "sha512-mNAgZ1GmyNhD7AuqnTG3/VQ26o760+ZYBPKjPvugO8+nLbYfX6TVpJPseBvopbdY+qpZ/lKUnmEc1LeZYS3QAA=="}, "node_modules/lru-cache": {"version": "7.18.3", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz", "integrity": "sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA==", "engines": {"node": ">=12"}}, "node_modules/lru.min": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/lru.min/-/lru.min-1.1.2.tgz", "integrity": "sha512-Nv9KddBcQSlQopmBHXSsZVY5xsdlZkdH/Iey0BlcBYggMd4two7cZnKOK9vmy3nY0O5RGH99z1PCeTpPqszUYg==", "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wellwelwel"}}, "node_modules/mailtrap": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/mailtrap/-/mailtrap-4.1.0.tgz", "integrity": "sha512-rCuumv0ZcLvxMukV8Pn9sh5hmk2TL23THrpwgE/yXwDfeJQdJGVENa2rNzGR9zcGsUK3LSXuyvjKrTwc21RBOQ==", "license": "MIT", "dependencies": {"axios": ">=0.27"}, "engines": {"node": ">=16.20.1", "yarn": ">=1.22.17"}, "peerDependencies": {"@types/nodemailer": "^6.4.9", "nodemailer": "^6.9.4"}, "peerDependenciesMeta": {"@types/nodemailer": {"optional": true}, "nodemailer": {"optional": true}}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "engines": {"node": ">= 0.4"}}, "node_modules/media-typer": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-1.1.0.tgz", "integrity": "sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==", "engines": {"node": ">= 0.8"}}, "node_modules/merge-descriptors": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-2.0.0.tgz", "integrity": "sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g==", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/mime-db": {"version": "1.54.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz", "integrity": "sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-3.0.1.tgz", "integrity": "sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==", "dependencies": {"mime-db": "^1.54.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "node_modules/mysql2": {"version": "3.14.1", "resolved": "https://registry.npmjs.org/mysql2/-/mysql2-3.14.1.tgz", "integrity": "sha512-7ytuPQJjQB8TNAYX/H2yhL+iQOnIBjAMam361R7UAL0lOVXWjtdrmoL9HYKqKoLp/8UUTRcvo1QPvK9KL7wA8w==", "dependencies": {"aws-ssl-profiles": "^1.1.1", "denque": "^2.1.0", "generate-function": "^2.3.1", "iconv-lite": "^0.6.3", "long": "^5.2.1", "lru.min": "^1.0.0", "named-placeholders": "^1.1.3", "seq-queue": "^0.0.5", "sqlstring": "^2.3.2"}, "engines": {"node": ">= 8.0"}}, "node_modules/named-placeholders": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-1.1.3.tgz", "integrity": "sha512-eLoBxg6wE/rZkJPhU/xRX1WTpkFEwDJEN96oxFrTsqBdbT5ec295Q+CoHrL9IT0DipqKhmGcaZmwOt8OON5x1w==", "dependencies": {"lru-cache": "^7.14.1"}, "engines": {"node": ">=12.0.0"}}, "node_modules/negotiator": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-1.0.0.tgz", "integrity": "sha512-8Ofs/AUQh8MaEcrlq5xOX0CQ9ypTF5dl78mjlMNfOK08fzpgTHQRQPBxcPlEtIw0yRpws+Zo/3r+5WRby7u3Gg==", "engines": {"node": ">= 0.6"}}, "node_modules/node-cron": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/node-cron/-/node-cron-3.0.3.tgz", "integrity": "sha512-dOal67//nohNgYWb+nWmg5dkFdIwDm8EpeGYMekPMrngV3637lqnX0lbUcCtgibHTz6SEz7DAIjKvKDFYCnO1A==", "license": "ISC", "dependencies": {"uuid": "8.3.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/node-device-detector": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/node-device-detector/-/node-device-detector-2.2.1.tgz", "integrity": "sha512-c+Om6Fs+FrDA5XQlCwFccKaxWUbJBeAqha6BmeJMVPPWTnfFJ5GHc3G6TCZkLFf77xqXw8pHB4Fo/lJ6TrG+9A==", "license": "MIT", "engines": {"node": ">= 10.x", "npm": ">= 6.x"}}, "node_modules/node-forge": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/node-forge/-/node-forge-1.3.1.tgz", "integrity": "sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==", "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "engines": {"node": ">= 6.13.0"}}, "node_modules/nodemailer": {"version": "6.10.1", "resolved": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.10.1.tgz", "integrity": "sha512-Z+iLaBGVaSjbIzQ4pX6XV41HrooLsQ10ZWPUehGmuantvzWoDVBnmsdUcOIDM1t+yPor5pDhVlDESgOMEGxhHA==", "license": "MIT-0", "engines": {"node": ">=6.0.0"}}, "node_modules/npm": {"version": "2.15.12", "resolved": "https://registry.npmjs.org/npm/-/npm-2.15.12.tgz", "integrity": "sha512-WMoAJ518W0vHjWy1abYnTeyG9YQpSoYGPxAx7d0C0L7U7Jo44bZsrvTjccmDohCJGxpasdKfqsKsl6o/RUPx6A==", "bundleDependencies": ["abbrev", "ansi", "ansi-regex", "ansicolors", "ansistyles", "archy", "async-some", "block-stream", "char-spinner", "chmodr", "chownr", "cmd-shim", "columnify", "config-chain", "dezalgo", "editor", "fs-vacuum", "fs-write-stream-atomic", "fstream", "fstream-npm", "github-url-from-git", "github-url-from-username-repo", "glob", "graceful-fs", "hosted-git-info", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inflight", "inherits", "ini", "init-package-json", "lockfile", "lru-cache", "minimatch", "mkdirp", "node-gyp", "nopt", "normalize-git-url", "normalize-package-data", "npm-cache-filename", "npm-install-checks", "npm-package-arg", "npm-registry-client", "npm-user-validate", "npmlog", "once", "opener", "osenv", "path-is-inside", "read", "read-installed", "read-package-json", "readable-stream", "realize-package-specifier", "request", "retry", "<PERSON><PERSON><PERSON>", "semver", "sha", "slide", "sorted-object", "spdx-license-ids", "strip-ansi", "tar", "text-table", "uid-number", "umask", "validate-npm-package-license", "validate-npm-package-name", "which", "wrappy", "write-file-atomic"], "license": "Artistic-2.0", "dependencies": {"abbrev": "~1.0.9", "ansi": "~0.3.1", "ansi-regex": "*", "ansicolors": "~0.3.2", "ansistyles": "~0.1.3", "archy": "~1.0.0", "async-some": "~1.0.2", "block-stream": "0.0.9", "char-spinner": "~1.0.1", "chmodr": "~1.0.2", "chownr": "~1.0.1", "cmd-shim": "~2.0.2", "columnify": "~1.5.4", "config-chain": "~1.1.10", "dezalgo": "~1.0.3", "editor": "~1.0.0", "fs-vacuum": "~1.2.9", "fs-write-stream-atomic": "~1.0.8", "fstream": "~1.0.10", "fstream-npm": "~1.1.1", "github-url-from-git": "~1.4.0", "github-url-from-username-repo": "~1.0.2", "glob": "~7.0.6", "graceful-fs": "~4.1.6", "hosted-git-info": "~2.1.5", "imurmurhash": "*", "inflight": "~1.0.4", "inherits": "~2.0.3", "ini": "~1.3.4", "init-package-json": "~1.9.4", "lockfile": "~1.0.1", "lru-cache": "~4.0.1", "minimatch": "~3.0.3", "mkdirp": "~0.5.1", "node-gyp": "~3.6.0", "nopt": "~3.0.6", "normalize-git-url": "~3.0.2", "normalize-package-data": "~2.3.5", "npm-cache-filename": "~1.0.2", "npm-install-checks": "~1.0.7", "npm-package-arg": "~4.1.0", "npm-registry-client": "~7.2.1", "npm-user-validate": "~0.1.5", "npmlog": "~2.0.4", "once": "~1.4.0", "opener": "~1.4.1", "osenv": "~0.1.3", "path-is-inside": "~1.0.0", "read": "~1.0.7", "read-installed": "~4.0.3", "read-package-json": "~2.0.4", "readable-stream": "~2.1.5", "realize-package-specifier": "~3.0.1", "request": "~2.74.0", "retry": "~0.10.0", "rimraf": "~2.5.4", "semver": "~5.1.0", "sha": "~2.0.1", "slide": "~1.1.6", "sorted-object": "~2.0.0", "spdx-license-ids": "~1.2.2", "strip-ansi": "~3.0.1", "tar": "~2.2.1", "text-table": "~0.2.0", "uid-number": "0.0.6", "umask": "~1.1.0", "validate-npm-package-license": "~3.0.1", "validate-npm-package-name": "~2.2.2", "which": "~1.2.11", "wrappy": "~1.0.2", "write-file-atomic": "~1.1.4"}, "bin": {"npm": "bin/npm-cli.js"}}, "node_modules/npm/node_modules/abbrev": {"version": "1.0.9", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/ansi": {"version": "0.3.1", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/ansi-regex": {"version": "2.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/ansicolors": {"version": "0.3.2", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/ansistyles": {"version": "0.1.3", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/archy": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/async-some": {"version": "1.0.2", "inBundle": true, "license": "ISC", "dependencies": {"dezalgo": "^1.0.2"}}, "node_modules/npm/node_modules/block-stream": {"version": "0.0.9", "inBundle": true, "license": "ISC", "dependencies": {"inherits": "~2.0.0"}, "engines": {"node": "0.4 || >=0.5.8"}}, "node_modules/npm/node_modules/char-spinner": {"version": "1.0.1", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/chmodr": {"version": "1.0.2", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/chownr": {"version": "1.0.1", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/cmd-shim": {"version": "2.0.2", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"graceful-fs": "^4.1.2", "mkdirp": "~0.5.0"}}, "node_modules/npm/node_modules/columnify": {"version": "1.5.4", "inBundle": true, "license": "MIT", "dependencies": {"strip-ansi": "^3.0.0", "wcwidth": "^1.0.0"}}, "node_modules/npm/node_modules/columnify/node_modules/wcwidth": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"defaults": "^1.0.0"}}, "node_modules/npm/node_modules/columnify/node_modules/wcwidth/node_modules/defaults": {"version": "1.0.3", "inBundle": true, "license": "MIT", "dependencies": {"clone": "^1.0.2"}}, "node_modules/npm/node_modules/columnify/node_modules/wcwidth/node_modules/defaults/node_modules/clone": {"version": "1.0.2", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/npm/node_modules/config-chain": {"version": "1.1.10", "inBundle": true, "dependencies": {"ini": "^1.3.4", "proto-list": "~1.2.1"}}, "node_modules/npm/node_modules/config-chain/node_modules/proto-list": {"version": "1.2.4", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/dezalgo": {"version": "1.0.3", "inBundle": true, "license": "ISC", "dependencies": {"asap": "^2.0.0", "wrappy": "1"}}, "node_modules/npm/node_modules/dezalgo/node_modules/asap": {"version": "2.0.3", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/editor": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/fs-vacuum": {"version": "1.2.9", "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "path-is-inside": "^1.0.1", "rimraf": "^2.5.2"}}, "node_modules/npm/node_modules/fs-write-stream-atomic": {"version": "1.0.8", "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}}, "node_modules/npm/node_modules/fs-write-stream-atomic/node_modules/iferr": {"version": "0.1.5", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/fstream": {"version": "1.0.10", "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "inherits": "~2.0.0", "mkdirp": ">=0.5 0", "rimraf": "2"}, "engines": {"node": ">=0.6"}}, "node_modules/npm/node_modules/fstream-npm": {"version": "1.1.1", "inBundle": true, "license": "ISC", "dependencies": {"fstream-ignore": "^1.0.0", "inherits": "2"}}, "node_modules/npm/node_modules/fstream-npm/node_modules/fstream-ignore": {"version": "1.0.5", "inBundle": true, "license": "ISC", "dependencies": {"fstream": "^1.0.0", "inherits": "2", "minimatch": "^3.0.0"}}, "node_modules/npm/node_modules/github-url-from-git": {"version": "1.4.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/github-url-from-username-repo": {"version": "1.0.2", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/npm/node_modules/glob": {"version": "7.0.6", "inBundle": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.2", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/npm/node_modules/glob/node_modules/fs.realpath": {"version": "1.0.0", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/glob/node_modules/path-is-absolute": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/graceful-fs": {"version": "4.1.6", "inBundle": true, "license": "ISC", "engines": {"node": ">=0.4.0"}}, "node_modules/npm/node_modules/hosted-git-info": {"version": "2.1.5", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/imurmurhash": {"version": "0.1.4", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/npm/node_modules/inflight": {"version": "1.0.5", "inBundle": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/npm/node_modules/inherits": {"version": "2.0.3", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/ini": {"version": "1.3.4", "inBundle": true, "license": "ISC", "engines": {"node": "*"}}, "node_modules/npm/node_modules/init-package-json": {"version": "1.9.4", "inBundle": true, "license": "ISC", "dependencies": {"glob": "^6.0.0", "npm-package-arg": "^4.0.0", "promzard": "^0.3.0", "read": "~1.0.1", "read-package-json": "1 || 2", "semver": "2.x || 3.x || 4 || 5", "validate-npm-package-license": "^3.0.1", "validate-npm-package-name": "^2.0.1"}}, "node_modules/npm/node_modules/init-package-json/node_modules/glob": {"version": "6.0.4", "inBundle": true, "license": "ISC", "dependencies": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "2 || 3", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/npm/node_modules/init-package-json/node_modules/glob/node_modules/path-is-absolute": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/init-package-json/node_modules/promzard": {"version": "0.3.0", "inBundle": true, "license": "ISC", "dependencies": {"read": "1"}}, "node_modules/npm/node_modules/lockfile": {"version": "1.0.1", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/lru-cache": {"version": "4.0.1", "inBundle": true, "license": "ISC", "dependencies": {"pseudomap": "^1.0.1", "yallist": "^2.0.0"}}, "node_modules/npm/node_modules/lru-cache/node_modules/pseudomap": {"version": "1.0.2", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/lru-cache/node_modules/yallist": {"version": "2.0.0", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/minimatch": {"version": "3.0.3", "inBundle": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/npm/node_modules/minimatch/node_modules/brace-expansion": {"version": "1.1.6", "inBundle": true, "license": "MIT", "dependencies": {"balanced-match": "^0.4.1", "concat-map": "0.0.1"}}, "node_modules/npm/node_modules/minimatch/node_modules/brace-expansion/node_modules/balanced-match": {"version": "0.4.2", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/minimatch/node_modules/brace-expansion/node_modules/concat-map": {"version": "0.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/mkdirp": {"version": "0.5.1", "inBundle": true, "license": "MIT", "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/npm/node_modules/mkdirp/node_modules/minimist": {"version": "0.0.8", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/node-gyp": {"version": "3.6.0", "inBundle": true, "license": "MIT", "dependencies": {"fstream": "^1.0.0", "glob": "^7.0.3", "graceful-fs": "^4.1.2", "minimatch": "^3.0.2", "mkdirp": "^0.5.0", "nopt": "2 || 3", "npmlog": "0 || 1 || 2 || 3 || 4", "osenv": "0", "request": "2", "rimraf": "2", "semver": "~5.3.0", "tar": "^2.0.0", "which": "1"}, "bin": {"node-gyp": "bin/node-gyp.js"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/npm/node_modules/node-gyp/node_modules/semver": {"version": "5.3.0", "inBundle": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/npm/node_modules/nopt": {"version": "3.0.6", "inBundle": true, "license": "ISC", "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/npm/node_modules/normalize-git-url": {"version": "3.0.2", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/normalize-package-data": {"version": "2.3.5", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^2.1.4", "is-builtin-module": "^1.0.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/npm/node_modules/normalize-package-data/node_modules/is-builtin-module": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"builtin-modules": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/normalize-package-data/node_modules/is-builtin-module/node_modules/builtin-modules": {"version": "1.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/npm-cache-filename": {"version": "1.0.2", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/npm-install-checks": {"version": "1.0.7", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"npmlog": "0.1 || 1 || 2", "semver": "^2.3.0 || 3.x || 4 || 5"}}, "node_modules/npm/node_modules/npm-package-arg": {"version": "4.1.0", "inBundle": true, "license": "ISC", "dependencies": {"hosted-git-info": "^2.1.4", "semver": "4 || 5"}}, "node_modules/npm/node_modules/npm-registry-client": {"version": "7.2.1", "inBundle": true, "license": "ISC", "dependencies": {"concat-stream": "^1.5.2", "graceful-fs": "^4.1.6", "normalize-package-data": "~1.0.1 || ^2.0.0", "npm-package-arg": "^3.0.0 || ^4.0.0", "once": "^1.3.3", "request": "^2.74.0", "retry": "^0.10.0", "semver": "2 >=2.2.1 || 3.x || 4 || 5", "slide": "^1.1.3"}, "optionalDependencies": {"npmlog": "~2.0.0 || ~3.1.0"}}, "node_modules/npm/node_modules/npm-registry-client/node_modules/concat-stream": {"version": "1.5.2", "engines": ["node >= 0.8"], "inBundle": true, "license": "MIT", "dependencies": {"inherits": "~2.0.1", "readable-stream": "~2.0.0", "typedarray": "~0.0.5"}}, "node_modules/npm/node_modules/npm-registry-client/node_modules/concat-stream/node_modules/readable-stream": {"version": "2.0.6", "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1"}}, "node_modules/npm/node_modules/npm-registry-client/node_modules/concat-stream/node_modules/readable-stream/node_modules/core-util-is": {"version": "1.0.2", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/npm-registry-client/node_modules/concat-stream/node_modules/readable-stream/node_modules/isarray": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/npm-registry-client/node_modules/concat-stream/node_modules/readable-stream/node_modules/process-nextick-args": {"version": "1.0.7", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/npm-registry-client/node_modules/concat-stream/node_modules/readable-stream/node_modules/string_decoder": {"version": "0.10.31", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/npm-registry-client/node_modules/concat-stream/node_modules/readable-stream/node_modules/util-deprecate": {"version": "1.0.2", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/npm-registry-client/node_modules/concat-stream/node_modules/typedarray": {"version": "0.0.6", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/npm-registry-client/node_modules/retry": {"version": "0.10.0", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/npm/node_modules/npm-user-validate": {"version": "0.1.5", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/npm/node_modules/npmlog": {"version": "2.0.4", "inBundle": true, "license": "ISC", "dependencies": {"ansi": "~0.3.1", "are-we-there-yet": "~1.1.2", "gauge": "~1.2.5"}}, "node_modules/npm/node_modules/npmlog/node_modules/are-we-there-yet": {"version": "1.1.2", "inBundle": true, "license": "ISC", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.0 || ^1.1.13"}}, "node_modules/npm/node_modules/npmlog/node_modules/are-we-there-yet/node_modules/delegates": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/npmlog/node_modules/gauge": {"version": "1.2.7", "inBundle": true, "license": "ISC", "dependencies": {"ansi": "^0.3.0", "has-unicode": "^2.0.0", "lodash.pad": "^4.1.0", "lodash.padend": "^4.1.0", "lodash.padstart": "^4.1.0"}}, "node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/has-unicode": {"version": "2.0.0", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/lodash._baseslice": {"version": "4.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/lodash._basetostring": {"version": "4.12.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/lodash.pad": {"version": "4.4.0", "inBundle": true, "license": "MIT", "dependencies": {"lodash._baseslice": "~4.0.0", "lodash._basetostring": "~4.12.0", "lodash.tostring": "^4.0.0"}}, "node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/lodash.padend": {"version": "4.5.0", "inBundle": true, "license": "MIT", "dependencies": {"lodash._baseslice": "~4.0.0", "lodash._basetostring": "~4.12.0", "lodash.tostring": "^4.0.0"}}, "node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/lodash.padstart": {"version": "4.5.0", "inBundle": true, "license": "MIT", "dependencies": {"lodash._baseslice": "~4.0.0", "lodash._basetostring": "~4.12.0", "lodash.tostring": "^4.0.0"}}, "node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/lodash.tostring": {"version": "4.1.4", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/once": {"version": "1.4.0", "inBundle": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/npm/node_modules/opener": {"version": "1.4.1", "inBundle": true, "license": "WTFPL", "bin": {"opener": "opener.js"}}, "node_modules/npm/node_modules/osenv": {"version": "0.1.3", "inBundle": true, "license": "ISC", "dependencies": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "node_modules/npm/node_modules/osenv/node_modules/os-homedir": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/osenv/node_modules/os-tmpdir": {"version": "1.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/path-is-inside": {"version": "1.0.1", "inBundle": true, "license": "WTFPL"}, "node_modules/npm/node_modules/read": {"version": "1.0.7", "inBundle": true, "license": "ISC", "dependencies": {"mute-stream": "~0.0.4"}, "engines": {"node": ">=0.8"}}, "node_modules/npm/node_modules/read-installed": {"version": "4.0.3", "inBundle": true, "license": "ISC", "dependencies": {"debuglog": "^1.0.1", "read-package-json": "^2.0.0", "readdir-scoped-modules": "^1.0.0", "semver": "2 || 3 || 4 || 5", "slide": "~1.1.3", "util-extend": "^1.0.1"}, "optionalDependencies": {"graceful-fs": "^4.1.2"}}, "node_modules/npm/node_modules/read-installed/node_modules/debuglog": {"version": "1.0.1", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/npm/node_modules/read-installed/node_modules/readdir-scoped-modules": {"version": "1.0.2", "inBundle": true, "license": "ISC", "dependencies": {"debuglog": "^1.0.1", "dezalgo": "^1.0.0", "graceful-fs": "^4.1.2", "once": "^1.3.0"}}, "node_modules/npm/node_modules/read-installed/node_modules/util-extend": {"version": "1.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/read-package-json": {"version": "2.0.4", "inBundle": true, "license": "ISC", "dependencies": {"glob": "^6.0.0", "json-parse-helpfulerror": "^1.0.2", "normalize-package-data": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.2"}}, "node_modules/npm/node_modules/read-package-json/node_modules/glob": {"version": "6.0.4", "inBundle": true, "license": "ISC", "dependencies": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "2 || 3", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/npm/node_modules/read-package-json/node_modules/glob/node_modules/path-is-absolute": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/read-package-json/node_modules/json-parse-helpfulerror": {"version": "1.0.3", "inBundle": true, "license": "MIT", "dependencies": {"jju": "^1.1.0"}}, "node_modules/npm/node_modules/read-package-json/node_modules/json-parse-helpfulerror/node_modules/jju": {"version": "1.3.0", "inBundle": true, "license": "WTFPL"}, "node_modules/npm/node_modules/read/node_modules/mute-stream": {"version": "0.0.5", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/readable-stream": {"version": "2.1.5", "inBundle": true, "license": "MIT", "dependencies": {"buffer-shims": "^1.0.0", "core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1"}}, "node_modules/npm/node_modules/readable-stream/node_modules/buffer-shims": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/readable-stream/node_modules/core-util-is": {"version": "1.0.2", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/readable-stream/node_modules/isarray": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/readable-stream/node_modules/process-nextick-args": {"version": "1.0.7", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/readable-stream/node_modules/string_decoder": {"version": "0.10.31", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/readable-stream/node_modules/util-deprecate": {"version": "1.0.2", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/realize-package-specifier": {"version": "3.0.1", "inBundle": true, "license": "ISC", "dependencies": {"dezalgo": "^1.0.1", "npm-package-arg": "^4.0.0"}}, "node_modules/npm/node_modules/request": {"version": "2.74.0", "inBundle": true, "license": "Apache-2.0", "dependencies": {"aws-sign2": "~0.6.0", "aws4": "^1.2.1", "bl": "~1.1.2", "caseless": "~0.11.0", "combined-stream": "~1.0.5", "extend": "~3.0.0", "forever-agent": "~0.6.1", "form-data": "~1.0.0-rc4", "har-validator": "~2.0.6", "hawk": "~3.1.3", "http-signature": "~1.1.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.7", "node-uuid": "~1.4.7", "oauth-sign": "~0.8.1", "qs": "~6.2.0", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/npm/node_modules/request/node_modules/aws-sign2": {"version": "0.6.0", "inBundle": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/npm/node_modules/request/node_modules/aws4": {"version": "1.4.1", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/bl": {"version": "1.1.2", "inBundle": true, "license": "MIT", "dependencies": {"readable-stream": "~2.0.5"}}, "node_modules/npm/node_modules/request/node_modules/bl/node_modules/readable-stream": {"version": "2.0.6", "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1"}}, "node_modules/npm/node_modules/request/node_modules/bl/node_modules/readable-stream/node_modules/core-util-is": {"version": "1.0.2", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/bl/node_modules/readable-stream/node_modules/isarray": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/bl/node_modules/readable-stream/node_modules/process-nextick-args": {"version": "1.0.7", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/bl/node_modules/readable-stream/node_modules/string_decoder": {"version": "0.10.31", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/bl/node_modules/readable-stream/node_modules/util-deprecate": {"version": "1.0.2", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/caseless": {"version": "0.11.0", "inBundle": true, "license": "Apache-2.0"}, "node_modules/npm/node_modules/request/node_modules/combined-stream": {"version": "1.0.5", "inBundle": true, "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/npm/node_modules/request/node_modules/combined-stream/node_modules/delayed-stream": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/npm/node_modules/request/node_modules/extend": {"version": "3.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/forever-agent": {"version": "0.6.1", "inBundle": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/npm/node_modules/request/node_modules/form-data": {"version": "1.0.0-rc4", "inBundle": true, "license": "MIT", "dependencies": {"async": "^1.5.2", "combined-stream": "^1.0.5", "mime-types": "^2.1.10"}, "engines": {"node": ">= 0.10"}}, "node_modules/npm/node_modules/request/node_modules/form-data/node_modules/async": {"version": "1.5.2", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/har-validator": {"version": "2.0.6", "inBundle": true, "license": "ISC", "dependencies": {"chalk": "^1.1.1", "commander": "^2.9.0", "is-my-json-valid": "^2.12.4", "pinkie-promise": "^2.0.0"}, "bin": {"har-validator": "bin/har-validator"}, "engines": {"node": ">=0.10"}}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/chalk": {"version": "1.1.3", "inBundle": true, "license": "MIT", "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/chalk/node_modules/ansi-styles": {"version": "2.2.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/chalk/node_modules/escape-string-regexp": {"version": "1.0.5", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/chalk/node_modules/has-ansi": {"version": "2.0.0", "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/chalk/node_modules/supports-color": {"version": "2.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/commander": {"version": "2.9.0", "inBundle": true, "license": "MIT", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "engines": {"node": ">= 0.6.x"}}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/commander/node_modules/graceful-readlink": {"version": "1.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/is-my-json-valid": {"version": "2.13.1", "inBundle": true, "license": "MIT", "dependencies": {"generate-function": "^2.0.0", "generate-object-property": "^1.1.0", "jsonpointer": "2.0.0", "xtend": "^4.0.0"}}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/is-my-json-valid/node_modules/generate-function": {"version": "2.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/is-my-json-valid/node_modules/generate-object-property": {"version": "1.2.0", "inBundle": true, "license": "MIT", "dependencies": {"is-property": "^1.0.0"}}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/is-my-json-valid/node_modules/generate-object-property/node_modules/is-property": {"version": "1.0.2", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/is-my-json-valid/node_modules/jsonpointer": {"version": "2.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.6.0"}}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/is-my-json-valid/node_modules/xtend": {"version": "4.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/pinkie-promise": {"version": "2.0.1", "inBundle": true, "license": "MIT", "dependencies": {"pinkie": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/pinkie-promise/node_modules/pinkie": {"version": "2.0.4", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/request/node_modules/hawk": {"version": "3.1.3", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"boom": "2.x.x", "cryptiles": "2.x.x", "hoek": "2.x.x", "sntp": "1.x.x"}, "engines": {"node": ">=0.10.32"}}, "node_modules/npm/node_modules/request/node_modules/hawk/node_modules/boom": {"version": "2.10.1", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"hoek": "2.x.x"}, "engines": {"node": ">=0.10.40"}}, "node_modules/npm/node_modules/request/node_modules/hawk/node_modules/cryptiles": {"version": "2.0.5", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"boom": "2.x.x"}, "engines": {"node": ">=0.10.40"}}, "node_modules/npm/node_modules/request/node_modules/hawk/node_modules/hoek": {"version": "2.16.3", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.40"}}, "node_modules/npm/node_modules/request/node_modules/hawk/node_modules/sntp": {"version": "1.0.9", "inBundle": true, "dependencies": {"hoek": "2.x.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/npm/node_modules/request/node_modules/http-signature": {"version": "1.1.1", "inBundle": true, "license": "MIT", "dependencies": {"assert-plus": "^0.2.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/assert-plus": {"version": "0.2.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/jsprim": {"version": "1.3.0", "engines": ["node >=0.6.0"], "inBundle": true, "license": "MIT", "dependencies": {"extsprintf": "1.0.2", "json-schema": "0.2.2", "verror": "1.3.6"}}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/jsprim/node_modules/extsprintf": {"version": "1.0.2", "engines": ["node >=0.6.0"], "inBundle": true}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/jsprim/node_modules/json-schema": {"version": "0.2.2", "inBundle": true}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/jsprim/node_modules/verror": {"version": "1.3.6", "engines": ["node >=0.6.0"], "inBundle": true, "dependencies": {"extsprintf": "1.0.2"}}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk": {"version": "1.9.2", "inBundle": true, "license": "MIT", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "dashdash": "^1.12.0", "getpass": "^0.1.1"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}, "optionalDependencies": {"ecc-jsbn": "~0.1.1", "jodid25519": "^1.0.0", "jsbn": "~0.1.0", "tweetnacl": "~0.13.0"}}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/asn1": {"version": "0.2.3", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/assert-plus": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/dashdash": {"version": "1.14.0", "inBundle": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/ecc-jsbn": {"version": "0.1.1", "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"jsbn": "~0.1.0"}}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/getpass": {"version": "0.1.6", "inBundle": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/jodid25519": {"version": "1.0.2", "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"jsbn": "~0.1.0"}}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/jsbn": {"version": "0.1.0", "inBundle": true, "license": "BSD", "optional": true}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/tweetnacl": {"version": "0.13.3", "inBundle": true, "license": "Public domain", "optional": true}, "node_modules/npm/node_modules/request/node_modules/is-typedarray": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/isstream": {"version": "0.1.2", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/json-stringify-safe": {"version": "5.0.1", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/request/node_modules/mime-types": {"version": "2.1.11", "inBundle": true, "license": "MIT", "dependencies": {"mime-db": "~1.23.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/npm/node_modules/request/node_modules/mime-types/node_modules/mime-db": {"version": "1.23.0", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/npm/node_modules/request/node_modules/node-uuid": {"version": "1.4.7", "inBundle": true, "bin": {"uuid": "bin/uuid"}}, "node_modules/npm/node_modules/request/node_modules/oauth-sign": {"version": "0.8.2", "inBundle": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/npm/node_modules/request/node_modules/qs": {"version": "6.2.1", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/npm/node_modules/request/node_modules/stringstream": {"version": "0.0.5", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/request/node_modules/tough-cookie": {"version": "2.3.1", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.8"}}, "node_modules/npm/node_modules/request/node_modules/tunnel-agent": {"version": "0.4.3", "inBundle": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/npm/node_modules/retry": {"version": "0.10.0", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/npm/node_modules/rimraf": {"version": "2.5.4", "inBundle": true, "license": "ISC", "dependencies": {"glob": "^7.0.5"}, "bin": {"rimraf": "bin.js"}}, "node_modules/npm/node_modules/semver": {"version": "5.1.0", "inBundle": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/npm/node_modules/sha": {"version": "2.0.1", "inBundle": true, "license": "(BSD-2-<PERSON><PERSON> OR MIT)", "dependencies": {"graceful-fs": "^4.1.2", "readable-stream": "^2.0.2"}}, "node_modules/npm/node_modules/sha/node_modules/readable-stream": {"version": "2.0.2", "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "process-nextick-args": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1"}}, "node_modules/npm/node_modules/sha/node_modules/readable-stream/node_modules/core-util-is": {"version": "1.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/sha/node_modules/readable-stream/node_modules/isarray": {"version": "0.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/sha/node_modules/readable-stream/node_modules/process-nextick-args": {"version": "1.0.3", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/sha/node_modules/readable-stream/node_modules/string_decoder": {"version": "0.10.31", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/sha/node_modules/readable-stream/node_modules/util-deprecate": {"version": "1.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/slide": {"version": "1.1.6", "inBundle": true, "license": "ISC", "engines": {"node": "*"}}, "node_modules/npm/node_modules/sorted-object": {"version": "2.0.0", "inBundle": true, "license": "WTFPL"}, "node_modules/npm/node_modules/spdx-license-ids": {"version": "1.2.2", "inBundle": true, "license": "Unlicense"}, "node_modules/npm/node_modules/strip-ansi": {"version": "3.0.1", "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/tar": {"version": "2.2.1", "inBundle": true, "license": "ISC", "dependencies": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}}, "node_modules/npm/node_modules/text-table": {"version": "0.2.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/uid-number": {"version": "0.0.6", "inBundle": true, "license": "ISC", "engines": {"node": "*"}}, "node_modules/npm/node_modules/umask": {"version": "1.1.0", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/validate-npm-package-license": {"version": "3.0.1", "inBundle": true, "license": "Apache-2.0", "dependencies": {"spdx-correct": "~1.0.0", "spdx-expression-parse": "~1.0.0"}}, "node_modules/npm/node_modules/validate-npm-package-license/node_modules/spdx-correct": {"version": "1.0.2", "inBundle": true, "license": "Apache-2.0", "dependencies": {"spdx-license-ids": "^1.0.2"}}, "node_modules/npm/node_modules/validate-npm-package-license/node_modules/spdx-expression-parse": {"version": "1.0.2", "inBundle": true, "license": "(MIT AND CC-BY-3.0)", "dependencies": {"spdx-exceptions": "^1.0.4", "spdx-license-ids": "^1.0.0"}}, "node_modules/npm/node_modules/validate-npm-package-license/node_modules/spdx-expression-parse/node_modules/spdx-exceptions": {"version": "1.0.4", "inBundle": true, "license": "CC-BY-3.0"}, "node_modules/npm/node_modules/validate-npm-package-name": {"version": "2.2.2", "inBundle": true, "license": "ISC", "dependencies": {"builtins": "0.0.7"}}, "node_modules/npm/node_modules/validate-npm-package-name/node_modules/builtins": {"version": "0.0.7", "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/which": {"version": "1.2.11", "inBundle": true, "license": "ISC", "dependencies": {"isexe": "^1.1.1"}, "bin": {"which": "bin/which"}}, "node_modules/npm/node_modules/which/node_modules/isexe": {"version": "1.1.2", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/wrappy": {"version": "1.0.2", "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/write-file-atomic": {"version": "1.1.4", "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "imurmurhash": "^0.1.4", "slide": "^1.1.5"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz", "integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz", "integrity": "sha512-pZA<PERSON>+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dependencies": {"wrappy": "1"}}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==", "engines": {"node": ">= 0.8"}}, "node_modules/passport": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/passport/-/passport-0.7.0.tgz", "integrity": "sha512-cPLl+qZpSc+ireUvt+IzqbED1cHHkDoVYMo30jbJIdOOjQ1MQYZBPiNvmi8UM6lJuOpTPXJGZQk0DtC4y61MYQ==", "dependencies": {"passport-strategy": "1.x.x", "pause": "0.0.1", "utils-merge": "^1.0.1"}, "engines": {"node": ">= 0.4.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/jared<PERSON>son"}}, "node_modules/passport-local": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/passport-local/-/passport-local-1.0.0.tgz", "integrity": "sha512-9wCE6qKznvf9mQYYbgJ3sVOHmCWoUNMVFoZzNoznmISbhnNNPhN9xfY3sLmScHMetEJeoY7CXwfhCe7argfQow==", "dependencies": {"passport-strategy": "1.x.x"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/passport-strategy": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/passport-strategy/-/passport-strategy-1.0.0.tgz", "integrity": "sha512-CB97UUvDKJde2V0KDWWB3lyf6PC3FaZP7YxZ2G8OAtn9p4HI9j9JLP9qjOGZFvyl8uwNT8qM+hGnz/n16NI7oA==", "engines": {"node": ">= 0.4.0"}}, "node_modules/path-to-regexp": {"version": "8.2.0", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-8.2.0.tgz", "integrity": "sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ==", "engines": {"node": ">=16"}}, "node_modules/pause": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/pause/-/pause-0.0.1.tgz", "integrity": "sha512-KG8UEiEVkR3wGEb4m5yZkVCzigAD+cVEJck2CzYZO37ZGJfctvVptVO192MwrtPhzONn6go8ylnOdMhKqi4nfg=="}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==", "license": "MIT"}, "node_modules/pvtsutils": {"version": "1.3.6", "resolved": "https://registry.npmjs.org/pvtsutils/-/pvtsutils-1.3.6.tgz", "integrity": "sha512-PLgQXQ6H2FWCaeRak8vvk1GW462lMxB5s3Jm673N82zI4vqtVUPuZdffdZbPDFRoU8kAhItWFtPCWiPpp4/EDg==", "license": "MIT", "dependencies": {"tslib": "^2.8.1"}}, "node_modules/pvutils": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/pvutils/-/pvutils-1.1.3.tgz", "integrity": "sha512-pMpnA0qRdFp32b1sJl1wOJNxZLQ2cbQx+k6tjNtZ8CpvVhNqEPRgivZ2WOUev2YMajecdH7ctUPDvEe87nariQ==", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/qs": {"version": "6.14.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.14.0.tgz", "integrity": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/random-bytes": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/random-bytes/-/random-bytes-1.0.0.tgz", "integrity": "sha512-iv7LhNVO047HzYR3InF6pUcUsPQiHTM1Qal51DcGSuZFBil1aBBWG5eHPNek7bvILMaYJ/8RU1e8w1AMdHmLQQ==", "engines": {"node": ">= 0.8"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-3.0.0.tgz", "integrity": "sha512-RmkhL8CAyCRPXCE28MMH0z2PNWQBNk2Q09ZdxM9IOOXwxwZbN+qbWaatPkdkWIKL2ZVDImrN/pK5HTRz2PcS4g==", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.6.3", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/reflect-metadata": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/reflect-metadata/-/reflect-metadata-0.2.2.tgz", "integrity": "sha512-urBwgfrvVP/eAyXx4hluJivBKzuEbSQs9rKWCrCkbSxNv8mxPcUZKeuoF3Uy4mJl3Lwprp6yy5/39VWigZ4K6Q==", "license": "Apache-2.0"}, "node_modules/request-ip": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/request-ip/-/request-ip-3.3.0.tgz", "integrity": "sha512-cA6Xh6e0fDBBBwH77SLJaJPBmD3nWVAcF9/XAcsrIHdjhFzFiB5aNQFytdjCGPezU3ROwrR11IddKAM08vohxA==", "license": "MIT"}, "node_modules/router": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/router/-/router-2.2.0.tgz", "integrity": "sha512-nLTrUKm2UyiL7rlhapu/Zl45FwNgkZGaCpZbIHajDYgwlJCOzLSk+cIPAnsEqV955GjILJnKbdQC1nVPz+gAYQ==", "dependencies": {"debug": "^4.4.0", "depd": "^2.0.0", "is-promise": "^4.0.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.0.0"}, "engines": {"node": ">= 18"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "node_modules/send": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/send/-/send-1.2.0.tgz", "integrity": "sha512-uaW0WwXKpL9blXE2o0bRhoL2EGXIrZxQ2ZQ4mgcfoBxdFmQold+qWsD2jLrfZ0trjKL6vOw0j//eAwcALFjKSw==", "dependencies": {"debug": "^4.3.5", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "fresh": "^2.0.0", "http-errors": "^2.0.0", "mime-types": "^3.0.1", "ms": "^2.1.3", "on-finished": "^2.4.1", "range-parser": "^1.2.1", "statuses": "^2.0.1"}, "engines": {"node": ">= 18"}}, "node_modules/seq-queue": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/seq-queue/-/seq-queue-0.0.5.tgz", "integrity": "sha512-hr3Wtp/GZIc/6DAGPDcV4/9WoZhjrkXsi5B/07QgX8tsdc6ilr7BFM6PM6rbdAX1kFSDYeZGLipIZZKyQP0O5Q=="}, "node_modules/serve-static": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-2.2.0.tgz", "integrity": "sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ==", "dependencies": {"encodeurl": "^2.0.0", "escape-html": "^1.0.3", "parseurl": "^1.3.3", "send": "^1.2.0"}, "engines": {"node": ">= 18"}}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw=="}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/sqlstring": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/sqlstring/-/sqlstring-2.3.3.tgz", "integrity": "sha512-qC9iz2FlN7DQl3+wjwn3802RTyjCx7sDvfQEXchwa6CWOx07/WVfh91gBmQ9fahw8snwGEWU3xGzOt4tFyHLxg==", "engines": {"node": ">= 0.6"}}, "node_modules/statuses": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "integrity": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==", "engines": {"node": ">= 0.8"}}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==", "engines": {"node": ">=0.6"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD"}, "node_modules/tsyringe": {"version": "4.10.0", "resolved": "https://registry.npmjs.org/tsyringe/-/tsyringe-4.10.0.tgz", "integrity": "sha512-axr3IdNuVIxnaK5XGEUFTu3YmAQ6lllgrvqfEoR16g/HGnYY/6We4oWENtAnzK6/LpJ2ur9PAb80RBt7/U4ugw==", "license": "MIT", "dependencies": {"tslib": "^1.9.3"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/tsyringe/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "license": "0BSD"}, "node_modules/type-is": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/type-is/-/type-is-2.0.1.tgz", "integrity": "sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==", "dependencies": {"content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/uid-safe": {"version": "2.1.5", "resolved": "https://registry.npmjs.org/uid-safe/-/uid-safe-2.1.5.tgz", "integrity": "sha512-KPHm4VL5dDXKz01UuEd88Df+KzynaohSL9fBh096KWAxSKZQDI2uBrVqtvRM4rwrIrRRKsdLNML/lnaaVSRioA==", "dependencies": {"random-bytes": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==", "engines": {"node": ">= 0.8"}}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "integrity": "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==", "engines": {"node": ">= 0.8"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="}, "node_modules/zerossl-client": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/zerossl-client/-/zerossl-client-1.1.1.tgz", "integrity": "sha512-OYJGgQt0OS32EACPwsrb818qlA6c6PQ0Msmvsg7GkQ+6PzU+TjkcczQ4Dd9bzND7gmZ/30Xw+6FgsRL0KWsvFA==", "license": "MIT", "dependencies": {"axios": "^1.6.8", "node-forge": "^1.3.1"}}}}