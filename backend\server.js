
/*-------------------------*/


//const ZeroSSL  = require('zerossl-client');
const express = require('express');
const mysql = require('mysql2/promise');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const session = require('express-session');
const passport = require('passport');
const bcrypt = require("bcryptjs");
const LocalStrategy = require('passport-local').Strategy;
const fs = require('fs');
const https = require('https');
const http = require('http');
const { getClientIp } = require('request-ip');
const DeviceDetector = require('node-device-detector');
/* ----------------SCHEDULE-----*/

const cron = require('node-cron');
cron.schedule('0 18 * * *',async () => {

  await <PERSON>Mail("Résumé – SmartLife").then(() => {
    console.log();
  }).catch((error) => {
    console.error("Error sending email:", error);
  });

});

/*----MAIL-----*/
const axios = require('axios');

const serviceID = 'service_teqsgz8';
const templateID = 'template_z4vyk8k';
const userID = 'I3I0Ss0kLJ5LwY6nX';//'Y_PTK_6mhQijIEjbyJVos'; // from EmailJS dashboard
//const accessToken = 'Y_PTK_6mhQijIEjbyJVos'; // optional if your account is configured that way
/*----------------*/
const authenticatedIPs = {};
let newIps = [];
let newIpsMail = [];
let Orders = [];
// Load environment variables
dotenv.config();
const app = express();
const PORT = process.env.PORT || 443;

// Middleware
app.use(cors());
app.use(express.json());
//app.use(express.static('public'));
app.use(session({
  secret: process.env.JWT_SECRET,
  resave: false,
  saveUninitialized: false
}));
app.use(passport.initialize());
app.use(passport.session());

/*----------------cert---------*/
const privateKey = fs.readFileSync(path.join(__dirname, 'cert', 'private.key'), 'utf8');
const certificate = fs.readFileSync(path.join(__dirname, 'cert', 'certificate.crt'), 'utf8');
const ca = fs.readFileSync(path.join(__dirname, 'cert', 'ca_bundle.crt'), 'utf8');

const credentials = {
  key: privateKey,
  cert: certificate,
  ca: ca
};

/*
const httpsOptions = {
  key: fs.readFileSync(path.join(__dirname, 'cert', 'server.key')),
  cert: fs.readFileSync(path.join(__dirname, 'cert', 'server.cert'))
};
*/
/*------------------end-----------------------*/
// MySQL pool
const pool = mysql.createPool({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'smartlife',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});
// Passport Local Strategy for Authentication
passport.use(new LocalStrategy(async (username, password, done) => {
  try {
    //console.log("log 1")
      const [rows] = await pool.query(
          "SELECT id, username, password, ip_address, confirmed FROM users WHERE username = ?", 
          [username]
      );

      if (rows.length === 0) {
          return done(null, false, { message: "Invalid username or password." });
      }

      const user = rows[0];

      // Verify password
      const match = await bcrypt.compare(password, user.password);
      if (!match) {
          return done(null, false, { message: "Invalid username or password." });
      }

      // Check if the account is confirmed by an admin
      if (user.confirmed !== 1) {
          return done(null, false, { message: "Your account is pending admin approval." });
      }

      return done(null, user);
  } catch (error) {
      return done(error);
  }
}));

passport.serializeUser((user, done) => {
done(null, user.id);
//console.log("log 2")
});

passport.deserializeUser(async (id, done) => {
try {
  //console.log("log 3")
    const [rows] = await pool.query(
        "SELECT id, username, email FROM users WHERE id = ?", 
        [id]
    );

    if (rows.length === 0) {
        return done(null, false);
    }

    done(null, rows[0]);
} catch (error) {
    done(error);
}
});
const isAuthenticated = (req, res, next) => {
  const IP_rem = getClientIp(req);
  const IP_add = filter(IP_rem);
  const now = Date.now();
  //console.log("log 4")
  // Check if user is authenticated via session
  if (!req.isAuthenticated()) {
      return res.redirect('/signin');
  }

  // Check if the IP is stored and within the 1-hour timeframe
  if (authenticatedIPs[IP_add] && now - authenticatedIPs[IP_add] < 3600000) {
      return next(); // Allow access
  }

  // If not authenticated, redirect to signin
  res.redirect('/signin');
};
/*const getClientIp = function(req) {
  return (req.headers["X-Forwarded-For"] ||
          req.headers["x-forwarded-for"] ||
          '').split(',')[0] ||
         req.client.remoteAddress;
};*/
function filter(adrr){
  const ipRegex = /(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/;
  const match = adrr.match(ipRegex)|| 0;
  return match[0] || 0;
}

//  device detection  
const detector = new DeviceDetector();

// ROUTES-------PAGES
app.get('google5605393b45c64d66.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'google5605393b45c64d66.html'));
});
app.get("/robots.txt",(req,res)=>{
  res.type('text/plain');
  res.sendFile(path.join(__dirname,"robots.txt"))
});
app.get("/sitemap.xml",(req,res)=>{
  //res.type('text/plain');
  res.sendFile(path.join(__dirname,"sitemap.xml"))
});
app.get('/', (req, res) => {
  const IP_rem = getClientIp(req);
  const IP_add = filter(IP_rem);

    const userAgent = req.headers['user-agent'];
    const result = detector.detect(userAgent);
    const deviceType = result.device?.type || 'unknown';
    if (!newIps.find(entry => entry.ip.split(':')[1].trim() == IP_add)) 
        {
            const former = deviceType + ' : '+IP_add;
            newIps.push({ip:former});
          let entry = newIpsMail.find(entry => entry.ip === IP_add);
          if (entry) {
            let previousDate = new Date(entry.date);
            let now = new Date();

            let diffInMs = now - previousDate;
            let diffInHours = diffInMs / (1000 * 60 * 60); // milliseconds to hours

            if (diffInMs >= 10) {
              entry.date = entry.date + ' | ' + getDateTime();
            }
          }
          else newIpsMail.push({ip:IP_add, device:deviceType, date: getDateTime()});
            
          setTimeout(() => {
              saveDataToFile();
            }, 2000);

        }
      else 
        {
          let entry = newIpsMail.find(entry => entry.ip === IP_add);
          if (entry) {
            let previousDate = new Date(entry.date);
            let now = new Date();

            let diffInMs = now - previousDate;
            let diffInHours = diffInMs / (1000 * 60 * 60); // milliseconds to hours

            if (diffInMs >= 10) {
              entry.date = entry.date + '\n' + getDateTime();
            }
            setTimeout(() => {
              saveDataToFile();
            }, 2000);
          }
        }
/*    const deviceBrand = result.device?.brand || 'unknown';
    const os = result.os?.name || 'unknown';
    const client = result.client?.name || 'unknown';
    if(IP_add == '*************'||IP_add =='0'){return res.sendFile(path.join(__dirname, 'public', 'index.html'));}
    else{  
    deviceType == 'desktop' ? res.sendFile(path.join(__dirname, 'public', 'index.html')) : res.sendFile(path.join(__dirname, 'public', 'mobile.html'));
    }
*/
   res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/cart', (req, res) => {
  const userAgent = req.headers['user-agent'];
  const result = detector.detect(userAgent);

  const deviceType = result.device?.type || 'unknown';
/*  const deviceBrand = result.device?.brand || 'unknown';
  const os = result.os?.name || 'unknown';
  const client = result.client?.name || 'unknown';   */
    //deviceType == 'desktop' ? res.sendFile(path.join(__dirname, 'public', 'cart.html')) : res.sendFile(path.join(__dirname, 'public', 'cartmobile.html'));
    res.sendFile(path.join(__dirname, 'public', 'cart.html'));
});

app.get('/order-success', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'order-success.html'));
});

app.get('/references', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'references.html'));
});


app.get('/Admin/login', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'Admin', 'login.html'));
});
app.get('/Admin/dashboard', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'Admin', 'dashboard.html'));
});
app.post('/Admin/signin', async (req, res) => {
  const { username, password } = req.body;
  const IP_rem = getClientIp(req);
  const IP_add = filter(IP_rem);

  try {
      // Fetch user by username
      const [rows] = await pool.query(
          "SELECT id, username,email, password FROM users WHERE username = ?", 
          [username]
      );

      if (rows.length === 0) {
          return res.status(400).json({ error: "Username error" });
      }

      const user = rows[0];

      // Verify password
      const match = await bcrypt.compare(password, user.password.trim());
      if (!match) {
          return res.status(400).json({ error: "Password error" });
      }
/*
      // Check if account is confirmed by admin
      if (user.confirmed !== 1) {
          return res.status(403).json({ error: "pending" });
      }

      // Ensure IP matches the one from signup
      if (user.ip_address !== IP_add) {
          return res.status(403).json({ error: "Unauthorized IP address" });
      }
*/
      // Authenticate user and store session
      req.login(user, (err) => {
          if (err) {
              return res.status(500).json({ error: "Authentication failed" });
          }

          // Store authenticated IP for 1 hour
          authenticatedIPs[IP_add] = Date.now();
          return res.json({ success: true, message: "Login successful" });
      });

  } catch (error) {
      console.error("Signin Error:", error);
      res.status(500).json({ error: "Server error" });
  }
});
    // Signup Route
  /*  
async function signup(){
      
      const username="Haythem"
      const password="Jeff588wake197"
      const email="<EMAIL>"
      //const { username,email, password } = req.body;

     
        // Hash the password before storing it
        const hashedPassword = await bcrypt.hash(password, 10);
        console.log("start")
        const insertUserQuery =
          "INSERT INTO users (username,email, password) VALUES (?,?, ?)";
       pool.query(insertUserQuery, [username,email, hashedPassword], (err) => {
          if (err) console.log({ error: "Database error" });
          console.log({ message: "User created successfully" });
        });
    
    };
   */ 
// ================= PRODUCTS =================

// Get all products
app.get('/products', async (req, res) => {
    try {
        const [products] = await pool.query('SELECT * FROM products');
        res.json(products);
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: 'Failed to fetch products' });
    }
});

// Create a product
app.post('/Admin/products',isAuthenticated, async (req, res) => {
  const { name, price, stock, image, fulldescription, protocol } = req.body;

  if (!name || price == null || stock == null) {
      return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
      const [result] = await pool.query(
          `INSERT INTO products (name, price, stock, image, fulldescription, protocol) 
           VALUES (?, ?, ?, ?, ?, ?)`,
          [name, price, stock, image || null, fulldescription || null, protocol || 'wifi']
      );
      res.status(201).json({ message: 'Product created', productId: result.insertId });
  } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Failed to create product' });
  }
});

app.get('/products/:id', async (req, res) => {
    const { id } = req.params;
    try {
      const result = await pool.query(
       'SELECT * from products WHERE id = ?',
        [id]
      );
      res.json(result[0]);
    } catch (err) {
      console.error(err);
      res.status(500).json({ error: 'Failed to update product' });
    }
  });

// Update a product
app.put('/Admin/products/:id',isAuthenticated, async (req, res) => {
  const { id } = req.params;
  const { name, price, stock, image, fulldescription, protocol } = req.body;

  try {
      const [result] = await pool.query(
          `UPDATE products 
           SET name = ?, price = ?, stock = ?, image = ?, fulldescription = ?, protocol = ? 
           WHERE id = ?`,
          [name, price, stock, image || null, fulldescription || null, protocol || 'wifi', id]
      );
      if (result.affectedRows === 0) {
          return res.status(404).json({ error: 'Product not found' });
      }
      res.json({ message: 'Product updated' });
  } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Failed to update product' });
  }
});
  
// Delete a product
app.delete('/Admin/products/:id',isAuthenticated, async (req, res) => {
  const { id } = req.params;
  try {
      const [result] = await pool.query('DELETE FROM products WHERE id = ?', [id]);
      if (result.affectedRows === 0) {
          return res.status(404).json({ error: 'Product not found' });
      }
      res.json({ message: 'Product deleted' });
  } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Failed to delete product' });
  }
});

/*================= videos =================*/
  app.get('/references_videos', async (req, res) => {
    try {
        const [videos] = await pool.query('SELECT * FROM references_videos');
        res.json(videos);
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: 'Failed to fetch videos' });
    }
});

// Create a new reference video
app.post('/Admin/references_videos',isAuthenticated, async (req, res) => {
  const { videos } = req.body;

  if (!videos) {
      return res.status(400).json({ error: 'Missing video data' });
  }

  try {
      const [result] = await pool.query(
          'INSERT INTO references_videos (video) VALUES (?)',
          [videos]
      );
      res.status(201).json({ message: 'Video reference added', id: result.insertId });
  } catch (error) {
      console.error('Error inserting reference video:', error);
      res.status(500).json({ error: 'Failed to insert reference video' });
  }
});

// ================= ORDERS =================

// Create order
app.post('/orders', async (req, res) => {
    try {
      const { products, customer } = req.body;
  
      if (!products || !Array.isArray(products) || products.length === 0) {
        return res.status(400).json({ message: 'Invalid products data.' });
      }
  
      if (!customer || !customer.name || !customer.address || !customer.phone) {
        return res.status(400).json({ message: 'Customer information is incomplete.' });
      }
  
      // Calculate total amount (you can customize this based on price lookup from DB if needed)
      let totalAmount = 0;
      const orderItems = [];
  
      for (const item of products) {
        if (!item.id || typeof item.quantity !== 'number') {
          return res.status(400).json({ message: 'Invalid product format.' });
        }
        
        // Optional: Fetch price from DB if you want to avoid trusting the frontend
        const [rows] = await pool.query('SELECT price FROM products WHERE id = ?', [item.id]);
        if (rows.length === 0) continue;
        totalAmount += parseFloat(rows[0].price) * item.quantity;
        orderItems.push(item);
      }
      totalAmount += 7; // Add shipping cost (you can customize this)
      // Store order
      const Cid = await pool.query(`
        INSERT INTO orders (clientName,clientPhone, clientAdress, orderitems, totalAmount)
        VALUES (?,?, ?, ?, ?)
      `, [
        customer.name,
        customer.phone,
        customer.address,
        JSON.stringify(orderItems),
        totalAmount
      ]);
      const orderId = Cid[0].insertId;
      Orders.push({
        id: orderId,
        items: orderItems,
        totalAmount,
        customer
      });
      console.log("Order placed: "+ orderId +' :', Orders);
      setTimeout(() => {
        saveDataToFile();
      }, 2000);
      res.status(201).json({ message: 'Order placed successfully.'});
    } catch (error) {
      console.error('Order error:', error);
      res.status(500).json({ message: 'Server error while placing order.' });
    }
  });
  /*
// Get all orders  /// waiting to be fixed
app.get('/Admin/orders',isAuthenticated, async (req, res) => {
    try {
        const [orders] = await pool.query(`
            SELECT o.id, o.userId, o.status, o.created_at,
                JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'productId', oi.product_id,
                        'quantity', oi.quantity,
                        'priceAtPurchase', oi.price_at_purchase
                    )
                ) AS items
            FROM orders o
            LEFT JOIN orderitems oi ON o.id = oi.order_id
            GROUP BY o.id
            ORDER BY o.created_at DESC
        `);

        // Parse the JSON items field
        const formattedOrders = orders.map(order => ({
            ...order,
            items: JSON.parse(order.items || '[]')
        }));

        res.json(formattedOrders);
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: 'Failed to fetch orders' });
    }
});
*/
// Get all orders
app.get('/Admin/orders',isAuthenticated, async (req, res) => {
    //console.log("Orders fetch");
  try {
        const [orders] = await pool.query('SELECT * FROM orders ORDER BY createdAt DESC');

        const formatted = orders.map(order => ({
            ...order,
            orderitems: JSON.parse(order.orderitems || '[]')
        }));
        //console.log("Orders fetched: ", formatted);
        res.json(formatted);
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: 'Failed to fetch orders' });
    }
});


// Update order status
app.patch('/Admin/orders/:id',isAuthenticated, async (req, res) => {
    const { id } = req.params;
    const { status } = req.body;
    if(status !== 'confirmed' && status !== 'shipped' && status !== 'delivered') {
        return res.status(400).json({ error: 'Invalid status' });
    }
    try {
        const [result] = await pool.query(`UPDATE orders SET status = ? WHERE id = ?`, [status, id]);
        if (result.affectedRows === 0) {
            return res.status(404).json({ error: 'Order not found' });
        }
        res.json({ message: 'Order status updated' });
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: 'Failed to update order' });
    }
});


app.get('/Admin/Messages',isAuthenticated,async (req,res) =>{
    try {
      const [result] = await pool.query(`SELECT * FROM contacts ORDER BY id DESC`);
      res.json(result);
  } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Failed' });
  }
})

// ================= USERS =================

// Favicon routes
app.get('/favicon.ico', (req, res) => {
  res.sendFile(path.join(__dirname, 'public/assets/favicon/favicon.ico'));
});

app.get('/favicon-96x96.png', (req, res) => {
  res.sendFile(path.join(__dirname, 'public/assets/favicon/favicon-96x96.png'));
});

app.get('/favicon.svg', (req, res) => {
  res.sendFile(path.join(__dirname, 'public/assets/favicon/favicon.svg'));
});

app.get('/apple-touch-icon.png', (req, res) => {
  res.sendFile(path.join(__dirname, 'public/assets/favicon/apple-touch-icon.png'));
});

app.get('/site.webmanifest', (req, res) => {
  res.sendFile(path.join(__dirname, 'public/assets/favicon/site.webmanifest'));
});

app.get('/logo', (req, res) => {
    res.sendFile(path.join(__dirname, 'public','assets','logo.png'));
});

app.post('/api/contact',async (req, res) => {
    const { name, email, message } = req.body;
  
    if (!name || !email || !message) {
      return res.status(400).json({ error: 'All fields are required.' });
    }
  
    try {
      const [result] = await pool.execute(
        'INSERT INTO contacts (name, email, message) VALUES (?, ?, ?)',
        [name, email, message]
      );
      res.status(200).json({ message: 'Contact message saved.', insertId: result.insertId });
    } catch (error) {
      console.error('Error saving contact:', error);
      res.status(500).json({ error: 'Failed to save contact message.' });
    }
  });

  
https.createServer(credentials, app).listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
});
/*
http.createServer(app).listen(3000, () => {
    console.log('HTTP server is running on port 3000');
});
// Redirect HTTP to HTTPS
/*
http.createServer((req, res) => {
  res.writeHead(301, { Location: `https://${req.headers.host}${req.url}` });
  res.end();
}).listen(449);
*/
// Insert newIps every 1 minute
setInterval(async () => {
  if(!newIps.length >0)return;
  const {orders,ips} = readDataFromFile();
  let ipsString = '' ;
  if (ips && ips.length > 0) {
    ipsString = ips.map(ipObject => ipObject.ip).join('|');
    //console.log(ipsString); // Output: "***************|**************|**************|*************|**************|***************|**************"
  } else {
    return
  }
  newIps = []; // clear the list right after preparing values

  try {
    await pool.query(
      'INSERT INTO contactips (IPs) VALUES (?)',
      [ipsString]
    );
    console.log(`Inserted ${ipsString} IP(s) into contactips.`);
    //saveDataToFile();
  } catch (err) {
    console.error('Error inserting IPs:', err);
  }
}, 60*60* 1000);



// ✅ HTML Generator Function
async function generateSmartLifeEmail(orders=[], ips=[]) {
    let orderRows = '', ipRows = '';
    //console.log(orders,ips)
  if(orders.length > 0){
        orderRows = orders.map(o =>
          `<tr>
            <td>${o.customer.name}</td>
            <td>${o.items.map(item => `${item.name} (${item.quantity})`).join('<br>')}</td>
            <td>${o.totalAmount}</td>
          </tr>`
        ).join("");
    }
    else {
        orderRows = `<tr><td> --------- </td><td> --------- </td><td> --------- </td></tr>`
    }
  if(ips.length > 0){
      ipRows = ips.map(ip =>
        `<tr><td>${ip.ip}</td><td>${ip.device}</td><td>${ip.date}</td></tr>`
      ).join("");
    }
    else{
      ipRows = `<tr><td> --------- </td><td> --------- </td><td> --------- </td></tr>`
    }

  return `
  <!doctype html>
  <html>
    <head>
      <meta charset="UTF-8">
      <style>
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
      </style>
    </head>
    <body style="font-family: sans-serif;">
      <h2>Résumé – SmartLife</h2>

      <h3>Commandes</h3>
      <table>
        <tr><th>Client</th><th>Articles</th><th>Total</th></tr>
        ${orderRows}
      </table>

      <h3>Connexions IP</h3>
      <table>
        <tr><th>Adresse IP</th><th>Horodatage</th></tr>
        ${ipRows}
      </table>

      <!--<p>Merci pour votre confiance,<br>– L’équipe SmartLife</p>-->
    </body>
  </html>
  `;
}

async function SendMail(subject){
  const {orders,ips} = readDataFromFile();
  //console.log("step1")
  const html = await generateSmartLifeEmail(orders,ips);
  //console.log("step2")
   
  const data = {
      service_id: serviceID,
      template_id: templateID,
      user_id: userID,
      template_params: {
        'subject': subject,
        'from_name': 'Smartlife-TN',
        'to_email': '<EMAIL>',
        'message': html
      }
    };

    try{
      const response = await axios.post('https://api.emailjs.com/api/v1.0/email/send', data)
      if (response.status === 200 && response.data === 'OK') {
        fs.writeFileSync('backupData.txt', '');
        console.log('✅ Email sent successfully and Data erased');
      } else {
        console.error('❌ Failed to send email:', response.data);
      }
    } catch (error) {
      console.error('❌ Error occurred:', error.message || error);
    }
    /*
  await axios.post('https://api.emailjs.com/api/v1.0/email/send', data)
      .then(response => ()=>{
      console.log('Success:', response.data)
      fs.writeFileSync('backupData.txt', '');
      console.log("Data erased after Sent.");
      })
      .catch(error => console.error('Error:', error));
*/    
}

function saveDataToFile() {
  let existingData = { orders: [], ips: [] };

  // If the file exists and is not empty, load existing data
  if (fs.existsSync('backupData.txt')) {
    const raw = fs.readFileSync('backupData.txt', 'utf-8').trim();
    if (raw) {
      try {
        existingData = JSON.parse(raw);
      } catch (e) {
        console.error("Error parsing existing data. Overwriting with new.");
      }
    }
  }

  // Merge current data
  const mergedData = {
    orders: [...existingData.orders, ...Orders],
    ips: [...existingData.ips, ...newIpsMail]
  };

  // Write merged data
  fs.writeFileSync('backupData.txt', JSON.stringify(mergedData, null, 2));
  console.log("Merged data saved to file.");

  // Optional: clear in-memory arrays after saving
  Orders = [];
  newIpsMail = [];
}


// Function to read data from file
function readDataFromFile() {
  const defaultData = { orders: [], ips: [] };

  if (fs.existsSync('backupData.txt')) {
    try {
      const rawData = fs.readFileSync('backupData.txt', 'utf-8');

      // Handle empty or whitespace-only content
      if (!rawData.trim()) {
        return defaultData;
      }

      const parsed = JSON.parse(rawData);
      const { orders = [], ips = [] } = parsed;
      return { orders, ips };

    } catch (error) {
      console.error('Error reading or parsing backupData.txt:', error);
      return defaultData;
    }
  }

  return defaultData;
}

function getDateTime(){
    let now = new Date();

  let options = {
    timeZone: 'Africa/Tunis', // Or 'Europe/Paris', both UTC+1
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  };

  let formatter = new Intl.DateTimeFormat('en-CA', options);
  let parts = formatter.formatToParts(now);

  let date = `${parts.find(p => p.type === 'year').value}-${parts.find(p => p.type === 'month').value}-${parts.find(p => p.type === 'day').value}`;
  let time = `${parts.find(p => p.type === 'hour').value}:${parts.find(p => p.type === 'minute').value}:${parts.find(p => p.type === 'second').value}`;

  let formatted = `${date} ${time}`;
  return formatted;  // "2025-05-20 14:35:12"

}