{"name": "zerossl-client", "version": "1.1.1", "description": "Unofficial Node client for Zero SSL", "type": "module", "main": "dist/index.js", "types": "dist/esm/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/chrisllontop/zerossl.git"}, "keywords": ["zero", "ssl", "http", "client"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/chrisllontop/zerossl/issues"}, "homepage": "https://github.com/chrisllontop/zerossl#readme", "devDependencies": {"@types/node-forge": "^1.3.11", "typescript": "^5.4.5", "xo": "^0.58.0"}, "dependencies": {"axios": "^1.6.8", "node-forge": "^1.3.1"}, "packageManager": "pnpm@9.1.0", "xo": {"rules": {"@typescript-eslint/naming-convention": "off", "import/extensions": "off"}}}