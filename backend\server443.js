const Ticket_Html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>GMAO Pro - Système de Tickets Moderne</title>

  <!-- Modern Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Bootstrap 5.3 -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <!-- jsPDF for PDF generation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>

<!-- html2canvas for chart images -->
<script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
  <style>
  :root {
    /* 🎨 MODERN TICKETING SYSTEM DESIGN */

    /* Primary Colors */
    --primary: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #8b5cf6;
    --secondary: #64748b;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #06b6d4;

    /* Backgrounds */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-card: rgba(255, 255, 255, 0.05);
    --bg-glass: rgba(255, 255, 255, 0.1);
    --bg-glass-strong: rgba(255, 255, 255, 0.15);

    /* Text Colors */
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --gradient-bg: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-12: 3rem;

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-mono: 'Space Grotesk', monospace;

    /* Transitions */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* 🌟 MODERN BODY & LAYOUT */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: var(--font-primary);
    background: var(--gradient-bg);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
  }

  /* 🎭 MODERN CONTAINER */
  .modern-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-6);
  }

  /* 🎨 MODERN HEADER */
  .modern-header {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    margin-bottom: var(--space-8);
    box-shadow: var(--shadow-lg);
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-4);
  }

  .header-title {
    display: flex;
    align-items: center;
    gap: var(--space-3);
  }

  .header-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: var(--shadow-glow);
  }

  .header-text h1 {
    font-size: 1.875rem;
    font-weight: 800;
    margin: 0;
    background: linear-gradient(135deg, var(--text-primary), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .header-text p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.875rem;
  }
  /* 🎫 MODERN TICKET CARDS */
  .ticket-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
  }

  .ticket-card, .cardi {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
  }

  .ticket-card::before, .cardi::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
  }

  .ticket-card:hover, .cardi:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
    border-color: rgba(255, 255, 255, 0.2);
  }

  .ticket-card:hover::before, .cardi:hover::before {
    opacity: 1;
  }

  /* 🏷️ MODERN CARD TITLE */
  .card-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4);
    background: var(--bg-glass-strong);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-4);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    color: var(--text-primary);
  }

  .card-title:hover {
    background: var(--bg-glass-strong);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }

  .card-title h5 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
  }

  /* 🎯 STATUS BADGES */
  .status-badge {
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
  }

  .status-open {
    background: var(--gradient-warning);
    color: white;
    animation: pulse 2s infinite;
  }

  .status-closed {
    background: var(--gradient-success);
    color: white;
  }

  .status-in-progress {
    background: var(--gradient-primary);
    color: white;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }
  /* 💬 MODERN COMMENT SYSTEM */
  .comment-box {
    background: var(--bg-glass);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    margin-top: var(--space-4);
    max-height: 0;
    opacity: 0;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(-10px);
  }

  .comment-box.show,
  .comment-box.active {
    max-height: 1000px;
    opacity: 1;
    transform: translateY(0);
    padding-top: var(--space-6);
    padding-bottom: var(--space-6);
  }

  .comment-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
  }

  .comment {
    background: var(--bg-glass-strong);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-bottom: var(--space-3);
    transition: var(--transition);
  }

  .comment:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  .comment-left {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.1));
    border-left: 3px solid var(--primary);
  }

  .comment-right {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.1));
    border-left: 3px solid var(--success);
    margin-left: auto;
    max-width: 80%;
  }

  .comment-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    margin-bottom: var(--space-1);
  }

  .comment-text {
    color: var(--text-secondary);
    line-height: 1.5;
  }

  /* 🚀 MODERN BUTTONS */
  .btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    position: relative;
    overflow: hidden;
  }

  .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
  }

  .btn:hover::before {
    left: 100%;
  }

  .btn:hover {
    transform: translateY(-2px);
  }

  .btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
  }

  .btn-primary:hover {
    box-shadow: var(--shadow-lg), var(--shadow-glow);
  }

  .btn-success {
    background: var(--gradient-success);
    color: white;
    box-shadow: var(--shadow-md);
  }

  .btn-warning {
    background: var(--gradient-warning);
    color: white;
    box-shadow: var(--shadow-md);
  }

  .btn-danger {
    background: var(--gradient-danger);
    color: white;
    box-shadow: var(--shadow-md);
  }

#ticketList {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 1rem;
  color: var(--light-bg);
}

.cardi {
    width: 48%;
    border-radius: 0.5rem;
    background-color: #010813;
    padding: 1rem;
    transition: width 0.5s ease, z-index 0.2s;
    z-index: 1;
    height: fit-content;
    box-shadow: 0 8px 20px rgba(15, 10, 10, 0.5), 0 2px 12px white !important;
}
.cardi:hover {
    transform: scale(1.03);  /* Tiny scale to trigger GPU promotion */
    z-index: 9999;
    border: 1px solid white; background-color: #010813;
}



.cardi:hover .btn-xl,
.cardi:hover .btn-xxl {
  display: inline-block;
}

.card-title {
    text-align: center;
    cursor: pointer;
    color: #b3b4f7;
    border: 1px solid WHITE;
    border-radius: 12px;
    box-shadow: 0 2px 9px white;
    padding: 0.5rem;
    margin-bottom: 20px !important;
}
.card-title:hover {
  background-color: #e9ecef;
  color: #010813;
}
#submit{
    background-color: #0d6efd;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    width: 100%;
    margin-top: 1.5rem;
}

.comment {
  width: 50%;
  padding: 10px 15px;
  border-radius: 20px;
  word-wrap: break-word;
  transition: width 1s ease;
}
.comment:hover{
    width: 75%;
}

.comment-left {
  text-align: left;
  background-color: #6ba6dd;
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 5px;
  /*max-width: 50%;*/
}

.comment-right {
  text-align: right;
  background-color: #a5dbc3;
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 5px;
  margin-left: auto;
  /*max-width: 50%;*/
}


.comment-name {
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 4px;
}
.comment-text{
  font-size: 0.9rem;
  line-height: 1.4;
  color: #5e5c5c;
}
.Pbadge{
    font-size: 1.2rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); 
    position: inherit;
    color:white;
    
}
.blinking {
    color: white !important;
    animation: blink 1s ease-in-out infinite;
}
@keyframes blink {
    0%, 100% {
        opacity: 1;
        box-shadow: 0px 0px 0px 2px white;
    }
    50% {
        opacity: 0.5;
        box-shadow: 0px 0px 1px 1px #010813;
    }
}
.btn-xxl{
    width:15%;
    display: none;
    transition: display 0.4s;
}
.btn-xxl i{
    font-size: 1.9rem;  
}
.btn-xl{
    font-size: 1.2rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    display: none;
  transition: display 0.3s;
}
.hidden {
    display: none;
}
.blue {
  border-left: 4px solid #4895ff;
}

.green {
  border-left: 8px solid #3bb54a;
}

.red {
  border-left: 8px solid #b3404a;
}
.yellow {
    border-left: 8px solid #f9c74f;
}
.purple {
    border-left: 8px solid #9d4edd;
}
.orange {
    border-left: 8px solid #f3722c;
}
.h5- p{
    position: absolute;
    text-align: center;
    
}
nav{
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 99.6vh;
    background-color: #18191ac7;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);    
    position: absolute;
    color: snow;
    top: 0.3vh;
    left: 0;
    width: 206px;
    padding: 10px 7px;
    z-index: 1000;
    border-right: 1px solid #747272;
    border-radius: 6px;
    transition: width 2s ease-in-out, display 0.8s ease-in-out;
    overflow: hidden;
}
nav button {
    width: 98%;
    margin-bottom: 30px;
    font-size: 1.2rem;
    padding: 10px;
    color: white;
    transition: background-color 0.3s, transform 0.3s;
    align-content: left;
}
nav button:hover {
    border: 1px solid #ffffff !important;
    transform: scale(1.05);
}
nav button i {
    font-size: 1.3rem;
    margin-right: 0.5rem;
}
h1 {
    font-size: 3.8rem;
    color: #0c0c0c;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 2.5rem;
    margin-top: 2.5rem;
}
.notranslate {
    unicode-bidi: isolate;
  }

  .time-display {
    display: flex;
    align-items: center;
    gap: 10px;
}
.time-display i {
    font-size: 1.5rem;
    color: #4BC0C0;
}
.time-display .value {
    font-size: 1.2rem;
    font-weight: bold;
}
.time-display .label {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
}

.status-open { color: #FF6384; font-weight: bold; }
.status-closed { color: #36A2EB; font-weight: bold; }

.table-responsive {
    max-height: 500px;
    overflow-y: auto;
}
.BULGE{
    animation: bulge .5s forwards;  
}

@media print {
    body * {
        visibility: hidden;
    }
    .modal-content {
        visibility: visible;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
}
@keyframes bulge {
    50% {
      transform: rotate(4deg);
      font-weight: bold;
    }
    100% {
      transform: rotate(0);
      font-weight: bold;
    }
}
  /* 🧭 MODERN NAVIGATION */
  .modern-nav {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    padding: var(--space-4);
    margin-bottom: var(--space-6);
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
    align-items: center;
  }

  .nav-btn {
    padding: var(--space-2) var(--space-4);
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--space-2);
  }

  .nav-btn:hover,
  .nav-btn.active {
    background: var(--bg-glass-strong);
    color: var(--text-primary);
    border-color: rgba(255, 255, 255, 0.2);
  }

  /* 📝 MODERN FORMS */
  .form-control {
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-4);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: var(--transition);
  }

  .form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    background: var(--bg-glass-strong);
  }

  .form-control::placeholder {
    color: var(--text-muted);
  }

  /* 📱 RESPONSIVE DESIGN */
  @media (max-width: 768px) {
    .modern-container {
      padding: var(--space-4);
    }

    .ticket-grid {
      grid-template-columns: 1fr;
      gap: var(--space-4);
    }

    .header-content {
      flex-direction: column;
      text-align: center;
    }
  }

  /* 🎭 FLOATING ELEMENTS */
  .floating-element {
    position: fixed;
    font-size: 2rem;
    opacity: 0.1;
    color: var(--primary);
    pointer-events: none;
    z-index: -1;
    animation: float 8s ease-in-out infinite;
  }

  .floating-element:nth-child(1) {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  .floating-element:nth-child(2) {
    top: 20%;
    right: 15%;
    animation-delay: 2s;
  }

  .floating-element:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
  }

  .floating-element:nth-child(4) {
    top: 60%;
    right: 25%;
    animation-delay: 6s;
  }

  .floating-element:nth-child(5) {
    bottom: 15%;
    right: 10%;
    animation-delay: 8s;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
      opacity: 0.1;
    }
    50% {
      transform: translateY(-20px) rotate(180deg);
      opacity: 0.2;
    }
  }

  /* 🎨 UTILITY CLASSES */
  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .glass-effect {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* 📋 INFO ITEMS */
  .info-item {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2);
    background: var(--bg-glass);
    border-radius: var(--radius-md);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .info-item i {
    font-size: 1.2rem;
  }

  /* 🎨 TICKET CONTENT */
  .ticket-content {
    padding: var(--space-4) 0;
  }

  .ticket-content h6 {
    font-weight: 600;
    margin-bottom: var(--space-2);
  }

  /* 🔄 CARD TOGGLE ANIMATION */
  .card-title i.bi-chevron-down {
    transition: transform 0.3s ease;
  }

  .card-title.active i.bi-chevron-down {
    transform: rotate(180deg);
  }

  /* 🎭 ENHANCED ANIMATIONS */
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .comment-box.show {
    animation: slideInUp 0.4s ease-out;
  }

  input[type="radio"] {
    transform: scale(1.5);
    margin-bottom: 10px;
    accent-color: var(--primary);
  }
  </style>
</head>
<body>
<!-- Floating Background Elements -->
<div class="floating-element">⚙️</div>
<div class="floating-element">🌡️</div>
<div class="floating-element">💧</div>
<div class="floating-element">🌪️</div>
<div class="floating-element">📊</div>
  <!-- 🏗️ Modern Container -->
  <div class="modern-container">

    <!-- 🎨 Modern Header -->
    <header class="modern-header">
      <div class="header-content">
        <div class="header-title">
          <div class="header-icon">
            <i class="bi bi-ticket-perforated"></i>
          </div>
          <div class="header-text">
            <h1>GMAO Pro</h1>
            <p>Système de Gestion de Tickets Moderne</p>
          </div>
        </div>
        <div class="header-actions">
          <button class="btn btn-primary" onclick="window.location.href='/'">
            <i class="bi bi-house"></i> Accueil
          </button>
          <button class="btn btn-danger" onclick="window.location.href='/logout'">
            <i class="bi bi-box-arrow-right"></i> Déconnexion
          </button>
        </div>
      </div>
      <div class="mt-3 text-center">
        <small class="text-muted">
          <strong>Utilisateur:</strong> <span id="userName"></span> |
          <strong>IP:</strong> <span id="ipAddress"></span>
        </small>
      </div>
    </header>

    <!-- 🧭 Modern Navigation -->
    <nav class="modern-nav">
      <button class="nav-btn active" onclick="showAllTickets()">
        <i class="bi bi-ticket-perforated"></i>
        Tous les Tickets
      </button>
      <button class="nav-btn" onclick="MyTickets()">
        <i class="bi bi-person"></i>
        Mes Tickets
      </button>
      <button class="nav-btn" data-bs-toggle="modal" data-bs-target="#TicketModal">
        <i class="bi bi-plus-circle"></i>
        Nouveau Ticket
      </button>
      <button class="nav-btn" data-bs-toggle="modal" data-bs-target="#MonsuelleModal">
        <i class="bi bi-clipboard-data"></i>
        Statistiques
      </button>
      <button class="nav-btn" data-bs-toggle="modal" data-bs-target="#CSVModal">
        <i class="bi bi-filetype-csv"></i>
        Export CSV
      </button>
    </nav>
  <div class="container py-6">
    <h1 class="text-center text-white" style="margin-bottom:6rem !important;">Gestion de Tickets</h1>
    
    <div class="d-flex justify-content-around flex-nowrap align-items-center mb-3">
    <div class="d-flex flex-wrap gap-3">
        <!-- Total Tickets -->
        <div class="card border-white" style="width: 15.5rem;">
            <div class="card-body text-center p-3"style="color: white;background-image: radial-gradient( circle 404px at 20.3% 15.9%,  rgba(0,79,255,1) 0%, rgba(0,240,255,1) 90% );">
                <div class="d-flex justify-content-center align-items-center gap-2 mb-2">
                    <i class="bi bi-ticket-detailed text-white fs-4"></i>
                    <span class="h5 mb-0 text-white">Total</span>
                </div>
                <span id="totalTickets" class="h2 fw-bold text-white">0</span>
            </div>
        </div>

        <!-- Open Tickets -->
        <div id="openedTicketsbody" class="card border-white" style="width: 15.5rem;">
            <div class="card-body text-center p-3"style="color:black;background-image: radial-gradient( circle farthest-corner at 10% 20%,  rgba(246,248,79,1) 0%, rgba(249,192,101,1) 90% );">
                <div class="d-flex justify-content-center align-items-center gap-2 mb-2">
                    <i class="bi bi-folder2-open text-black fs-4"></i>
                    <span class="h5 mb-0 text-black">Ouverts</span>
                </div>
                <span id="openedTickets" class="h2 fw-bold text-black">0</span>
            </div>
        </div>

        <!-- Closed Tickets -->
        <div class="card border-white" style="width: 15.5rem;">
            <div class="card-body text-center p-3" style="color:white;background-image: radial-gradient( circle farthest-corner at 10% 20%,  rgba(57,181,51,1) 0%, rgba(35,91,32,1) 90% );">
                <div class="d-flex justify-content-center align-items-center gap-2 mb-2">
                    <i class="bi bi-check-circle text-white fs-4"></i>
                    <span class="h5 mb-0 text-white">Fermés</span>
                </div>
                <span id="closedTickets" class="h2 fw-bold text-white">0</span>
            </div>
        </div>

        <!-- Average Time -->
        <div id="averageTimebody" class="card border-white" style="width: 15.5rem;">
            <div class="card-body text-center p-3" style="background-image: radial-gradient( circle farthest-corner at 10% 20%,  rgba(202,248,255,1) 0%, rgba(186,204,227,1) 51.2%, rgba(117,144,179,1) 100.1% );">
                <div class="d-flex justify-content-center align-items-center gap-2 mb-2">
                    <i class="bi bi-clock-history text-black fs-4"></i>
                    <span class="h5 mb-0 text-black">Moyen</span>
                </div>
                <span id="averageTime" class="h2 fw-bold text-black">0 heure</span>
            </div>
        </div>
    </div>

</div>
    <hr>


    <!-- Ticket Submission Form -->
    <div class="modal fade notranslate" id="TicketModal" tabindex="-1" aria-labelledby="TicketModalLabel" translate="no">
        <div class="modal-dialog modal-l modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="missionModalLabel">Nouveau Ticket</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
    <div class="card mb-5 shadow-sm">
      <div class="card-body">
        <form id="ticketForm">
          <div class="mb-3">
            <label for="system_type" class="form-label">Système</label>
            <input type="text" class="form-control" id="system_type" required />
          </div>
          <div class="mb-3">
            <label for="description" class="form-label">Description ( Max 50 charactères )</label>
            <input class="form-control" id="description" maxlength="50" required />
          </div>
          <div class="mb-3">
          <label for="emplacement" class="form-label">emplacement ( Max 50 charactères )</label>
          <input class="form-control" id="emplacement" maxlength="50" required />
        </div>
          <div id="checkboxes" class="mb-3" style="border: 1px solid rgb(206, 202, 202);border-radius: 5px; padding: 10px 10px 10px 10px;">
            <label class="form-label d-block mb-2">Service Concerné</label><br>
            <div style=" text-align:center;">
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="checkbox" name="tunis" id="tunis">
              <label class="form-check-label" for="subtunis">Tunis</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="checkbox" name="sfax" id="sfax">
              <label class="form-check-label" for="subsfax">Sfax</label>
            </div>
            <!--<div class="form-check form-check-inline">
              <input class="form-check-input" type="checkbox" name="sousse" id="sousse">
              <label class="form-check-label" for="sousse">Sousse</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="checkbox" name="jendouba" id="jendouba">
              <label class="form-check-label" for="jendouba">Jendouba</label>
            </div>-->
            <span id="alert" class="text" style="font-size: 0.8rem;color:red; display: block; margin-top: 5px;"></span>
          </div>
        </div>
          <!--<div class="mb-3 hidden">
            <label for="reporter_name" class="form-label">Nom du reporteur</label>
            <input type="text" class="form-control" id="reporter_name" required />
          </div>-->
          <button id="submit" type="submit" class="btn btn-primary">Valider</button>
        </form>
      </div>
    </div>
</div>
</div>
</div>
    </div>

    <div class="modal fade notranslate" id="CSVModal" tabindex="-1" aria-labelledby="CSVModalLabel" translate="no">
    <div class="modal-dialog modal-l modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="CSVModalLabel">Télécharger CSV</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="card mb-5 shadow-sm">
                <div class="card-body">
                    <form id="CSVForm">
                        <div class="mb-3">
                        <label for="startDate" class="form-label">Date Début</label>
                        <input type="date" name="FROM" class="form-control" id="startDate" required />
                        <label for="endDate" class="form-label">Date Fin</label>
                        <input type="date" name="TO" class="form-control" id="endDate" required />
                        </div>
                        <span id="alert1" class="text" style="font-size: 0.8rem;color:red; display: block; margin-top: 5px;"></span>
                        <button id="CSVSubmit" type="submit" class="btn btn-primary" style="position:relative;left:36%;">Télécharger</button>
                    </form>
                </div>
            </div>
            </div>
        </div>
        </div>
    </div>

    <div class="modal fade notranslate" id="MonsuelleModal" tabindex="-1" aria-labelledby="MonsuelleModalLabel" translate="no">
    <div class="modal-dialog modal-l modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Choisie un Mois</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="card mb-5 shadow-sm">
                    <div class="card-body">
                            <form id="MonthForm">
                            <!-- Month input and radio buttons -->
                            <div class="mb-3">
                                <label for="Mois" class="form-label">Mois</label>
                                <input type="month" name="recapMonth" class="form-control" id="Mois" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Représentation:</label>
                                <div class="d-flex flex-row justify-content-around"> 
                                    <div class="form-check">
                                        <input type="radio" id="table" name="representation" value="table" class="form-check-input">
                                        <label for="table" class="form-check-label">Tableau <i class="bi bi-table"></i></label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" id="graph" name="representation" value="graph" class="form-check-input" checked>
                                        <label for="graph" class="form-check-label">Graphique <i class="bi bi-bar-chart-line"></i> </label>
                                    </div>
                                </div>
                            </div>
                            <button id="MonthSubmit" type="submit" style="position: relative;left:35%;" class="btn btn-primary mt-3">Recherche</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <span id="alert2" class="text-danger small"></span>
            </div>
        </div>
    </div>
</div>

    <!-- 🎫 Modern Ticket Grid -->
    <div id="ticketList" class="ticket-grid">
        <!-- Tickets will be populated here -->
    </div>

  </div> <!-- End modern-container -->

  <!-- Modal -->
<div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="staticBackdropLabel"></h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">

  
      <!-- Chart Container -->
      <div id="chartContainer" class="mt-4">
          <div class="row">
              <div class="col-md-6">
                  <canvas id="statusChart"></canvas>
              </div>
              <div class="col-md-6">
                  <div class="card">
                      <div class="card-body">
                          <h5 class="card-title">Temps de résolution</h5>
                          <div id="avgTime"></div>
                      </div>
                  </div>
                  <div class="mt-4">
                      <canvas id="systemChart"></canvas>
                  </div>
              </div>
          </div>
      </div>
  
      <!-- Table Container -->
      <div id="tableContainer" class="mt-4" style="display: none;">
          <div class="table-responsive">
              <table id="ticketTable" class="table table-striped">
                  <thead>
                      <tr>
                          <th>ID</th>
                          <th>Système</th>
                          <th>Subdivision</th>
                          <th>Emplacement</th>
                          <th>Statut</th>
                          <th>Créé le</th>
                          <th>Temps de résolution</th>
                      </tr>
                  </thead>
                  <tbody></tbody>
              </table>
          </div>
      </div>
  </div>
      <div class="modal-footer">
        <button type="button" id="printRepresentation" class="btn btn-secondary"><i class="bi bi-printer"></i> Imprimer</button>
      </div>
    </div>
  </div>
</div>

  <script>
  document.addEventListener("keydown", e => {
    if (e.ctrlKey && e.key === "u") {
      alert("❌ C'est interdit de faire ça !");
      e.preventDefault();
    }
  });
  
  function OpenNavbar() {
    const navbar = document.getElementById('navbar');
    if (navbar.style.display === 'none' || navbar.style.display === '') {
      navbar.style.display = 'flex';
      navbar.style.width = '206px';
    } else {
      navbar.style.display = 'none';
      navbar.style.width = '0';
    }
  }
  //if clicked outside the navbar, close it
  document.addEventListener('click', function(event) {
    const navbar = document.getElementById('navbar');
    const toggler = document.querySelector('.navbar-toggler');
    if (!navbar.contains(event.target) && !toggler.contains(event.target)) {
      navbar.style.display = 'none';
      navbar.style.width = '0';
    }
  });
  
    const host = '';//"http://localhost:80";
    let TicketIds =[];
    let AllTickets = '';
    let Summarydata = {};
    const MyName = sessionStorage.getItem('Me') || 'Anonyme';
    document.getElementById('CSVForm').addEventListener('submit', async function (e) {
        e.preventDefault();
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
      
        const FROM = document.getElementById('startDate').value;
        const TO = document.getElementById('endDate').value;
        const alertEl = document.getElementById('alert1');
      
        // Reset alerts
        alertEl.textContent = "";
      
        // Validate dates
        if (!FROM || !TO) {
          alertEl.textContent = "* Veuillez remplir les deux dates.";
          submitBtn.disabled = false;
          return;
        }
      
        const fromDate = new Date(FROM + 'T00:00:00');
        const toDate = new Date(TO + 'T23:59:59');
      
        if (fromDate > toDate) {
          alertEl.textContent = "* date de début doit être antérieure à la date de fin.";
          submitBtn.disabled = false;
          return;
        }
      const queryString = new URLSearchParams({ FROM, TO }).toString();
        try {
          const response = await fetch(host + '/api/export/tickets.csv?'+queryString, {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' }
            //body: JSON.stringify({ FROM, TO })
          });
      
          if (!response.ok) {
            const error = await response.text();
            throw new Error(error || 'Erreur serveur');
          }
      
          const contentType = response.headers.get('Content-Type');
          if (!contentType || !contentType.includes('csv')) {
            throw new Error('* Réponse serveur invalide');
          }
      
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = \`tickets_\${FROM}_\${TO}.csv\`; // Dynamic filename
          document.body.appendChild(a);
          a.click();
          a.remove();
          window.URL.revokeObjectURL(url);
          // Close the modal
          const modal = bootstrap.Modal.getInstance(document.getElementById('CSVModal'));
          modal.hide();
      
        } catch (error) {
          console.error('Erreur:', error);
          alertEl.textContent = \`Erreur: \${error.message}\`;
          submitBtn.disabled = false;
        } 
      });

    document.getElementById('ticketForm').addEventListener('submit', async function (e) {
      e.preventDefault();
      const checkboxesnames = validateCheckboxes(e); // Validate checkboxes and get names
    if (checkboxesnames == false) { 
        document.getElementById('checkboxes').style.border = '2px solid red'; // Highlight checkboxes if validation fails
        return;
    }

      const ticket = {
        system_type: document.getElementById('system_type').value,
        description: document.getElementById('description').value,
        emplacement: document.getElementById('emplacement').value,
        Subdivision: checkboxesnames.join(' | ')
        //reported_by: document.getElementById('reporter_name').value
      };
      await fetch('/api/tickets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(ticket)
      });
      this.reset();
      loadTickets();
    });

    const user = sessionStorage.getItem('Me') || 'Anonyme';
    document.getElementById('userName').textContent = user;
    const Sessiondata = JSON.parse(sessionStorage.getItem("sessiondata")) || {};
    document.getElementById('ipAddress').textContent = Sessiondata.ip|| 'Inconnu';

    function validateCheckboxes(event) {
    const checkboxes = document.querySelectorAll('.form-check-input');
    let oneChecked = false;
        let checkedName = [];
    checkboxes.forEach((checkbox) => {
      if (checkbox.checked) {
        checkedName.push(checkbox.id);
        oneChecked = true;
      }
    });
    if (checkedName.length > 1) {
        document.getElementById('checkboxes').style.border = '2px solid red'; // Highlight checkboxes if no service is selected
        document.getElementById('alert').textContent = "* Veuillez cocher un seul service concerné.";
        event.preventDefault(); 
        return false;
      }
      if (!oneChecked) {
        event.preventDefault(); // stop form submission
        document.getElementById('alert').textContent = "* Veuillez cocher un service concerné.";
        return false;
      }
      document.getElementById('alert').textContent = "";
    return checkedName;
  }

  
  // Load Chart.js if not already loaded (add to HTML head)
 
  // DOM Elements
const monthForm = document.getElementById('MonthForm');
const chartContainer = document.getElementById('chartContainer');
const tableContainer = document.getElementById('tableContainer');
const statusChartCanvas = document.getElementById('statusChart');
const systemChartCanvas = document.getElementById('systemChart');
const avgTimeElement = document.getElementById('avgTime');
const alertElement = document.getElementById('alert2');

// Chart instances
let statusChart = null;
let systemChart = null;

// Main form handler
monthForm.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // Reset UI
    alertElement.textContent = '';
    showLoading(true);
    
    try {
        const formData = new FormData(monthForm);
        const month = formData.get('recapMonth');
        const representation = formData.get('representation');
        
        // Fetch data from server
        const { stats, tickets } = await fetchTicketData(month);
        
        // Format month for display (French locale)
        const monthDisplay = new Date(month + '-01').toLocaleDateString('fr-FR', {
            month: 'long',
            year: 'numeric'
        }).replace(' ', ' ');
        
        // Update UI based on representation choice
        if (representation === 'graph') {
            document.getElementById("staticBackdropLabel").textContent = \`Graphique - \${monthDisplay}\`;
            renderCharts(stats);
            showVisualization('chart');
        } else {
            document.getElementById("staticBackdropLabel").textContent = \`Tableau - \${monthDisplay}\`;
            renderTable(tickets);
            showVisualization('table');
        }
        
    } catch (error) {
        console.error("Error:", error);
        alertElement.textContent = 'Erreur lors de la récupération des données';
    } finally {
        const currentModal = bootstrap.Modal.getInstance(document.getElementById('MonsuelleModal'));
        const nextModal = new bootstrap.Modal(document.getElementById('staticBackdrop'));
        showLoading(false);
        wait(2000);
        // Hide current modal
        currentModal.hide();
        
        // When current modal is fully hidden, show next one
        document.getElementById('MonsuelleModal').addEventListener('hidden.bs.modal', function() {
            nextModal.show();
            
            // Remove the event listener to prevent memory leaks
            this.removeEventListener('hidden.bs.modal', arguments.callee);
        });
        
    }
});

// Fetch data from server
async function fetchTicketData(month) {
    const response = await fetch(\`/api/monsuelle?month=\${month}\`);
    
    if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new Error(error.message || 'Erreur serveur');
    }
    
    return await response.json();
}

// Render charts
function renderCharts(stats) {
    // IMPORTANT: Set white background for PDF export
    Chart.defaults.backgroundColor = '#FFFFFF';
    Chart.defaults.borderColor = '#DDDDDD';
    // Destroy existing charts
    if (statusChart) statusChart.destroy();
    if (systemChart) systemChart.destroy();
    
    // 1. Ticket Status Pie Chart
    statusChart = new Chart(statusChartCanvas, {
        type: 'pie',
        data: {
            labels: ['Ouverts', 'Fermés'],
            datasets: [{
                data: [stats.statusCount.open, stats.statusCount.closed],
                backgroundColor: ['#FF6384', '#36A2EB'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: { position: 'bottom' },
                tooltip: {
                    callbacks: {
                        label: (context) => {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((context.raw / total) * 100);
                            return \`\${context.label}: \${context.raw} (\${percentage}%)\`;
                        }
                    }
                }
            }
        }
    });
    
    // 2. System Type Bar Chart
    systemChart = new Chart(systemChartCanvas, {
        type: 'bar',
        data: {
            labels: Object.keys(stats.systemTypes),
            datasets: [{
                label: 'Tickets',
                data: Object.values(stats.systemTypes),
                backgroundColor: '#4BC0C0',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: { stepSize: 1 }
                }
            },
            plugins: {
                legend: { display: false }
            }
        }
    });
    
    // 3. Display average resolution time
    renderAverageTime(stats.avgResolutionHours);
}

// Render average time
function renderAverageTime(hours) {
    let timeText;
    
    if (hours > 0) {
        if (hours < 24) {
            timeText = \`\${hours.toFixed(1)} heures\`;
        } else {
            const days = Math.floor(hours / 24);
            const remainingHours = (hours % 24).toFixed(1);
            timeText = \`\${days}j \${remainingHours}h\`;
        }
    } else {
        timeText = "Aucun ticket résolu et fermé";
    }
    
    avgTimeElement.innerHTML = \`
        <div class="time-display">
            <i class="bi bi-clock"></i>
            <div>
                <span class="value">\${timeText}</span>
                <span class="label">Temps moyen de résolution</span>
            </div>
        </div>
    \`;
}

// Render table view
function renderTable(tickets) {
    const tableBody = document.querySelector('#ticketTable tbody');
    tableBody.innerHTML = '';
    
    tickets.forEach(ticket => {
        const row = document.createElement('tr');
        
        // Calculate resolution time if closed
        let resolutionTime = '';
        if (ticket.status === 'closed' && ticket.updated_at) {
            const hours = (new Date(ticket.updated_at) - new Date(ticket.created_at)) / (1000 * 60 * 60);
            resolutionTime = hours < 24 
                ? \`\${hours.toFixed(1)}h\` 
                : \`\${Math.floor(hours/24)}j \${(hours%24).toFixed(1)}h\`;
        }
        
        row.innerHTML = \`
            <td>\${ticket.id}</td>
            <td>\${ticket.system_type}</td>
            <td>\${ticket.subdivision}</td>
            <td>\${ticket.emplacement}</td>
            <td class="status-\${ticket.status}">\${ticket.status === 'open' ? 'Ouvert' : 'Fermé'}</td>
            <td>\${new Date(ticket.created_at).toLocaleDateString()}</td>
            <td>\${resolutionTime}</td>
        \`;
        
        tableBody.appendChild(row);
    });
}

// UI Helpers
function showVisualization(type) {
    chartContainer.style.display = type === 'chart' ? 'block' : 'none';
    tableContainer.style.display = type === 'table' ? 'block' : 'none';
}

function showLoading(show) {
    const submitBtn = document.getElementById('MonthSubmit');
    submitBtn.disabled = show;
    submitBtn.innerHTML = show 
        ? '<span class="spinner-border spinner-border-sm" role="status"></span> Chargement...'
        : 'Recherche';
}

// Initialize default month
window.addEventListener('DOMContentLoaded', () => {
    const now = new Date();
    const currentMonth = \`\${now.getFullYear()}-\${String(now.getMonth() + 1).padStart(2, '0')}\`;
    document.getElementById('Mois').value = currentMonth;
});

    async function loadTickets(Me) {
        let tickets,SumOfTickets=0,SumOfOpened=0,SumOfClosed=0,TraitementAverage=0;
  if(!Me){
        const res = await fetch(\`/api/tickets\`);
        tickets = await res.json();
        AllTickets = tickets;
        //console.log("AllTickets:", tickets);
    }
    else{tickets = Me}
  const container = document.getElementById('ticketList');
  container.innerHTML = '';

    const classes = [" green", " red", " blue", " yellow", " purple", " orange"];
    let classIndex = 0;
    TicketIds =[];
  for (const ticket of tickets) {
    const card = document.createElement('div');
    card.className = 'cardi mb-3 shadow-sm'+ classes[classIndex % classes.length];
    classIndex++;
    SumOfTickets++;
    ticket.status === 'closed' ? SumOfClosed++ : SumOfOpened++;
    (ticket.status === 'closed' && ticket.resolu === 'oui') ? TraitementAverage += (new Date(ticket.resolu_at) - new Date(ticket.created_at)) : TraitementAverage += 0;
    card.style.position = '';
    const ticketId = \`ticket-\${ticket.id}\`;
    const commentBoxId = \`comment-box-\${ticket.id}\`;

    card.innerHTML = \`
      <div class="card-body">
        <div class="card-title" data-ticket-id="\${ticket.id}" onclick="toggleCard(this)">
            <div>
                <h5 class="mb-1">#\${ticket.id} - \${ticket.system_type}</h5>
                <small class="text-muted">
                    <i class="bi bi-geo-alt"></i> \${ticket.emplacement}
                </small>
            </div>
            <div class="d-flex align-items-center gap-2">
                <span class="status-badge status-\${ticket.status}">
                    \${ticket.status === 'closed' ? '<i class="bi bi-lock"></i>' : ticket.status === 'open' ? '<i class="bi bi-unlock"></i>' : '<i class="bi bi-clock"></i>'}
                    \${ticket.status === 'closed' ? 'Fermé' : ticket.status === 'open' ? 'Ouvert' : 'En cours'}
                </span>
                <i class="bi bi-chevron-down"></i>
            </div>
        </div>

        <div class="ticket-content mb-4">
            <div class="mb-3">
                <h6 class="text-gradient mb-2">
                    <i class="bi bi-exclamation-triangle"></i> Description du problème
                </h6>
                <p class="text-secondary">\${ticket.description}</p>
            </div>

            <div class="row g-3">
                <div class="col-md-6">
                    <div class="d-flex align-items-center gap-2">
                        <i class="bi bi-person text-info"></i>
                        <div>
                            <small class="text-muted">Rapporté par</small>
                            <div class="fw-medium text-primary">\${ticket.reported_by}</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center gap-2">
                        <i class="bi bi-calendar text-warning"></i>
                        <div>
                            <small class="text-muted">Créé le</small>
                            <div class="fw-medium text-primary">\${ticket.created_at.split('.')[0].replace('T',' ')}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div id="\${commentBoxId}" class="comment-box">
            <div class="d-flex align-items-center gap-2 mb-3">
                <i class="bi bi-chat-dots text-primary"></i>
                <h6 class="mb-0 text-gradient">Commentaires</h6>
            </div>
            <div class="comment-container" id="comments-\${ticket.id}">
                <div class="text-muted text-center py-3">
                    <i class="bi bi-hourglass-split"></i> Chargement des commentaires...
                </div>
            </div>
      
        \${ticket.status !== 'closed' ? \`
          <form id="commentarea-\${ticket.id}" class="mt-2 hidden" onsubmit="submitComment(event, \${ticket.id})">
            <div class="mb-3 d-flex flex-row justify-content-center align-items-center">
              <textarea style="width:80%;margin-right:1%;" name="comment" class="form-control" placeholder="Ajouter un commentaire" rows="2" required></textarea>
              <button type="submit" title="envoyer" class="btn btn-outline-primary btn-xxl"><i class="bi bi-send"></i></button>
            </div>
            <div class="d-flex flex-column justify-content-center align-items-center mb-1"style="border:1px solid grey;border-radius:5px;box-shadow:inset -1px 0px 6px 2px #010813">
                <h6 id="drapeau-\${ticket.id}" class="BULGE" style="font-size: 1.2em !important;margin-top:16px;margin-bottom:16px;">Problème Résolu ?</h6>
                <div class="mb-3 d-flex flex-row justify-content-center justify-content-around align-items-center"style="gap:3rem">
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <input type="radio" name="resolu" id="resolu-\${ticket.id}-oui" class="me-3">
                        <label for="resolu-\${ticket.id}-oui" class="me-2">
                            <i class="bi bi-check-circle"></i> Oui
                        </label>
                    </div>
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <input type="radio" name="resolu" id="resolu-\${ticket.id}-non" checked>
                        <label for="resolu-\${ticket.id}-non" class="me-2">
                            <i class="bi bi-x-circle"></i> Non
                        </label>
                    </div>
                </div>
            </div>
          </form>
      
          <div class="btn-x d-flex justify-content-center align-items-center gap-2">
            <button type="button" onClick="showCommentArea('commentarea-\${ticket.id}', true)" class="btn btn-outline-warning btn-xl"><i class="bi bi-pen"></i> Commenter</button>
            <button type="button" onClick="CloseTicket('\${ticket.id}')" class="btn btn-outline-danger btn-xl"><i class="bi bi-x-octagon"></i> Fermer Ticket</button>
          </div>
        \` : \`
        <div class="d-flex flex-column"style="border:2px solid grey; border-radius:5px;padding-left:20px;">
          <span style="padding:5px;">
            \${ticket.status === 'closed'
              ? \`<i class="bi bi-tools"></i> Résolu Par : \${ticket.resolu_technicien} le \${new Date(ticket.resolu_at).toLocaleString('fr-FR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              })}</span>
              <span style="padding:5px;">
              <i class="bi bi-key"></i> Fermé par : \${ticket.updated_by} le \${new Date(ticket.updated_at).toLocaleString('fr-FR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              })}\`
              : ''}
          </span>
          </div>
        \`}
      </div>
      
        </div>
        <div class="d-flex justify-content-center align-items-center mt-3">
            <p style="color:white; font-size:1.3rem;margin-top:1.5rem;margin-bottom:-0.5rem;box-shadow: 0.5px 0.5px 1px 1px white;;padding: 6px;border-radius: 8px;">\${ticket.emplacement}</p>
        </div>
      </div>
      \`;
      TicketIds.push(ticket.id); // Store ticket ID for later use
  // Load comments only when collapsed section is shown
card.querySelector(\`[data-bs-target="#\${commentBoxId}"]\`).addEventListener('click', async () => {
  const commentBox = document.getElementById(\`comments-\${ticket.id}\`);
  if (!commentBox.dataset.loaded) {
    const commentRes = await fetch(\`\${host}/api/tickets/\${ticket.id}/comments\`);
    const comments = await commentRes.json();

    let lastCommenter = null;
    let lastDirection = 'left';
    let cr_date;

    commentBox.innerHTML = comments.length > 0
      ? comments.map(c => {
          if (c.commenter_name === lastCommenter) {
            // Same commenter, keep direction
          } else {
            // Switch direction for a new commenter
            lastDirection = lastDirection === 'left' ? 'right' : 'left';
            lastCommenter = c.commenter_name;
          }
          cr_date = new Date(c.created_at).toLocaleString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
          return \`
                <div class="comment comment-\${lastDirection}">
                <div class="comment-name">\${c.commenter_name}</div>
                    <div class="comment-text">\${c.comment}</div>
                <br>
                <p style="font-size:0.8rem;margin-top: 0.1rem;margin-bottom: -0.5rem;">\${cr_date}</p>
                </div>
            \`;
        }).join('')
      : '<p class="text-muted">Aucun commentaire.</p>';

    commentBox.dataset.loaded = true;
  }
});
    container.appendChild(card);
  }
  const averageTime = ((TraitementAverage / 1000) / 60) / SumOfClosed;
  const averageTimeFormatted = averageTime ? \`\${(averageTime/60).toFixed(1)} heures\` : 'Aucune donnée';
  Summarydata = {
    totalTickets: SumOfTickets,
    openedTickets: SumOfOpened,
    closedTickets: SumOfClosed,
    averageTime: averageTimeFormatted
  };
    document.getElementById('totalTickets').textContent = Summarydata.totalTickets;
    document.getElementById('openedTickets').textContent = Summarydata.openedTickets;
    if(Number(Summarydata.openedTickets) > 4) {document.getElementById('openedTicketsbody').classList.add("blinking");}
    document.getElementById('closedTickets').textContent = Summarydata.closedTickets;
    document.getElementById('averageTime').textContent = Summarydata.averageTime;
    if(Number(averageTimeFormatted.split('.')[0]) > 24) {
        const timeNumber = document.getElementById('averageTime');
        timeNumber.classList.replace('text-black', 'text-danger');
        document.getElementById('averageTimebody').classList.add("blinking");
    }
    else{document.getElementById('averageTime').style.color = 'black';}

  if (tickets.length === 0) {
    container.innerHTML = '<div class="text-center text-muted">Aucun ticket trouvé.</div>';
  }
}
async function CloseTicket(ticketId) {
    // Step 1: Confirm before closing
    const confirmClose = confirm("🛑 Êtes-vous sûr de vouloir fermer ce ticket ?");
    if (!confirmClose) return;
  
    // Step 2: Check if the ticket belongs to the current user
    const myTicket = AllTickets.find(ticket => 
      ticket.id === ticketId && ticket.reported_by === MyName
    );
  
    if (!myTicket) {
      alert("❌ Vous n'avez pas le droit de fermer ce ticket\\nouvert par une autre personne.");
      return;
    }
  
    let data = { status: 'closed' };
  
    // Step 3: If ticket is marked as not resolved, confirm again
    if (myTicket.resolu === 'non') {
      const confirmNonResolu = confirm("⚠️ Le problème est mentionné comme 'Non Résolu'.\\nÊtes-vous sûr de vouloir fermer ce ticket ?");
      if (!confirmNonResolu) return;
  
      const cause = prompt("💬 Veuillez indiquer la cause de non-résolution :");
      if (cause && cause.trim() !== "") {
        data.cause = cause.trim();
      } else {
        alert("❗ Fermeture annulée : cause de non résolution requise.");
        return;
      }
    }
  
    try {
      const res = await fetch(\`/api/tickets/\${ticketId}\`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
        if (res.ok) {
            alert("✅ Ticket fermé avec succès.");
            loadTickets();
        } else {
            alert("❌ Erreur lors de la fermeture du ticket.");
        }
    }catch(error){
        console.log(error);
        alert("❌ Erreur lors de la fermeture du ticket.");
    }
}

function showCommentArea(id, value) {
    console.log("showCommentArea id:", id);
    const commentArea = document.getElementById(id);
  
    if (value === true) {
      commentArea.classList.remove('hidden');
        const str = "drapeau-" + id.split('-')[1]
      const flagElement = document.getElementById(str);
      //console.log("flagElement", flagElement ,"   normalement",str);
      if (flagElement && !flagElement.classList.contains("flag")) {
        flagElement.classList.add("flag");
        //console.log("added : flag")
      }
  
      // Only run the interval once (optional improvement)
      if (!window._flagIntervalSet) {
        setInterval(() => {
          const flags = document.querySelectorAll('.flag'); // FIXED SELECTOR
          console.log("flags:", flags.length);
  
          flags.forEach(label => {
            label.classList.toggle("BULGE");
            const chars = label.textContent.split('');
            label.innerHtml = ''; // Clear the label
  
            chars.forEach(char => {
              label.innerHtml += \`\${char === ' ' ? '&nbsp;' : char}\`;
              wait(100);
            });
          });
        }, 2000);
        window._flagIntervalSet = true; // prevent multiple intervals
      }
  
    } else {
      TicketIds.forEach(ticketId => {
        const peace = id.split('-')[1];
        if (ticketId !== peace) {
          const otherCommentArea = document.getElementById(\`comment-box-\${ticketId}\`);
          if (otherCommentArea) {
            otherCommentArea.classList.remove('show');
          }
        }
      });
      commentArea?.classList?.add('hidden');
    }
  }
  

  async function submitComment(event, ticketId) {
    event.preventDefault();
  
    const form = event.target;
    const commentText = form.comment.value.trim();
  
    if (!commentText) return; // Prevent empty comments
  
    // Get selected radio button value
    let resolueValue = null;
    const radioOui = document.getElementById(\`resolu-\${ticketId}-oui\`);
    const radioNon = document.getElementById(\`resolu-\${ticketId}-non\`);
  
    if (radioOui && radioOui.checked) {
      resolueValue = "oui";
    } else if (radioNon && radioNon.checked) {
      resolueValue = "non";
    }
  
    const payload = {
      comment: commentText
    };
  
    if (resolueValue !== null) {
      payload.resolue = resolueValue;
    }
  
    try {
      const res = await fetch(\`/api/tickets/\${ticketId}/comments\`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
  
      if (!res.ok) throw new Error("Failed to post comment");
  
      form.reset();
      loadTickets();
    } catch (err) {
      console.error("Error submitting comment:", err);
      alert("❌ Erreur lors de l'envoi du commentaire.");
    }
  }
  
  
/*
    async function submitComment(event, ticketId) {
      event.preventDefault();
      const form = event.target;
      const comment = form.comment.value;
      const resolu_oui = document.getElementById("resolu-"+ticketId+"-oui") || false;
      const resolu_non = document.getElementById("resolu-"+ticketId+"-non") || false;
      if(resolu_oui && resolu_non){
        if(resolu_non.checked == true) comment.add("resolue":"non")
        else comment.add("resolue":"oui")
      }
      await fetch(\`/api/tickets/\${ticketId}/comments\`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ comment })
      });
      form.reset();
      loadTickets();
    }
*/
    function MyTickets() {
        const Me = AllTickets.filter(ticket => ticket.reported_by === MyName);
        if (Me.length === 0) {
            alert("❗ Vous n'avez pas de tickets.");
            return;
        }
        loadTickets(Me);
    }
    
    loadTickets();

    async function wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
      }

      document.getElementById('printRepresentation').addEventListener('click', async function() {
        const { jsPDF } = window.jspdf;
        
        try {
            // Create landscape PDF with better metadata
            const doc = new jsPDF({
                orientation: 'landscape',
                unit: 'mm',
                format: 'a4',
                filters: ['ASCIIHexEncode']
            });
            
            // Set document properties
            doc.setProperties({
                title: \`Rapport Tickets - \${document.getElementById('Mois').value}\`,
                subject: 'Statistiques des tickets',
                author: 'Portail Technique',
                keywords: 'tickets, statistiques, rapport',
                creator: 'Portail Technique'
            });
    
            const isChartView = document.getElementById('chartContainer').style.display !== 'none';
            
            if (isChartView) {
                await printChartsToPDF(doc);
            } else {
                await printTableToPDF(doc);
            }
    
            // Add secure footer
            addPDFFooter(doc);
            
            // Save with timestamp in filename
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:T]/g, '-');
            doc.save(\`rapport_tickets_\${timestamp}.pdf\`);
            
        } catch (error) {
            console.error('Échec de génération PDF:', error);
            alert('❌ Erreur lors de la génération du PDF: ' + error.message);
        }
    });
    
    // Print charts to PDF (optimized for landscape)
    async function printChartsToPDF(doc) {
        const pageWidth = doc.internal.pageSize.getWidth();
        const margin = 15;
        
        // Common header function
        const addHeader = () => {
            doc.setFont('helvetica', 'bold');
            doc.setFontSize(20);
            doc.setTextColor(40, 40, 40);
            doc.text('Rapport des Statistiques des Tickets', pageWidth / 2, margin, { align: 'center' });
            
            doc.setFontSize(12);
            doc.setFont('helvetica', 'normal');
            doc.text(\`Période: \${formatFrenchDate(document.getElementById('Mois').value)}\`, 
                     pageWidth / 2, margin + 10, { align: 'center' });
        };
    
        // Convert charts to images
        const [statusChartImg, systemChartImg] = await Promise.all([
            getEnhancedChartImage('#statusChart'),
            getEnhancedChartImage('#systemChart')
        ]);
    
        // PAGE 1: Status Chart
        addHeader();
        
        // Chart dimensions (80% of page height)
        const chartHeight = doc.internal.pageSize.getHeight() * 0.8;
        const chartWidth = chartHeight * 1.2;
        const chartX = (pageWidth - chartWidth) / 2;
        const chartX1 = (pageWidth - chartHeight) / 2;
        
        // Add status chart
        doc.addImage(statusChartImg, 'JPEG', chartX1, margin + 20, chartHeight, chartHeight);
        
        // Add statistics
        const statsY = margin + 20 + chartHeight + 10;
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text('Statistiques des Tickets:', margin, statsY);
        
        const openCount = getChartDataValue('#statusChart', 'Ouverts');
        const closedCount = getChartDataValue('#statusChart', 'Fermés');
        
        doc.setFontSize(11);
        doc.setFont('helvetica', 'normal');
        doc.text(\`• Tickets ouverts: \${openCount}\`, margin, statsY + 8);
        doc.text(\`• Tickets fermés: \${closedCount}\`, margin*3, statsY + 8);
    
        // PAGE 2: System Chart
        doc.addPage();
        addHeader();
        
        // Add system chart
        doc.addImage(systemChartImg, 'JPEG', chartX, margin + 20, chartWidth, chartHeight);
        
        // Add metrics
        const avgTimeText = document.getElementById('avgTime').textContent.trim();
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text('Métriques:', margin, statsY);
        
        doc.setFontSize(11);
        doc.setFont('helvetica', 'normal');
        doc.text(\`• Temps moyen de résolution: \${avgTimeText}\`, margin, statsY + 8);
    
        // Add footer to all pages
        doc.setFontSize(8);
        doc.setTextColor(100);
        const pageCount = doc.internal.getNumberOfPages();
        for (let i = 1; i <= pageCount; i++) {
            doc.setPage(i);
            doc.text(\`Page \${i}/\${pageCount}\`, margin/2, doc.internal.pageSize.height - 5, { align: 'left' });

        }
    }
    
    // Print table to PDF (optimized for landscape)
    async function printTableToPDF(doc) {
        const pageWidth = doc.internal.pageSize.getWidth();
        const margin = 15;
    
        // Header
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(20);
        doc.text('Détails des Tickets', pageWidth / 2, margin, { align: 'center' });
        
        // Subheader
        doc.setFontSize(12);
        doc.setFont('helvetica', 'normal');
        doc.text(\`Période: \${formatFrenchDate(document.getElementById('Mois').value)}\`, 
                 pageWidth / 2, margin + 10, { align: 'center' });
    
        // Get table data with error handling
        const { headers, rows } = await getTableData();
    
        // Generate table with autoTable
        doc.autoTable({
            head: [headers],
            body: rows,
            startY: margin + 20,
            styles: {
                fontSize: 9,
                cellPadding: 3,
                overflow: 'linebreak',
                halign: 'center',
                valign: 'middle'
            },
            columnStyles: {
                0: { cellWidth: 15, halign: 'center' }, // ID
                4: { halign: 'left' }, // Status
                5: { cellWidth: 25 }, // Date
                6: { cellWidth: 30 } // Resolution time
            },
            margin: { top: margin + 20 },
            didDrawPage: function(data) {
                // Page number
                doc.setFontSize(8);
                doc.setTextColor(100);
                doc.text(
                    \`Page \${data.pageNumber} sur \${data.pageCount}\`,
                    margin/2,
                    doc.internal.pageSize.height - 5,
                    { align: 'left' }
                );
                
                // Watermark on first page
                if (data.pageNumber === 1) {
                    doc.setFontSize(60);
                    doc.setTextColor(240, 240, 240);
                    doc.text(
                        '',
                        pageWidth / 2,
                        doc.internal.pageSize.getHeight() / 2,
                        { align: 'center', angle: 45 }
                    );
                    doc.setTextColor(0, 0, 0);
                }
            },
            tableLineWidth: 0.1,
            tableLineColor: 200
        });
    }
    
    // Helper functions
    function addPDFFooter(doc) {
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        
        doc.setFontSize(8);
        doc.setTextColor(100);
        doc.text(
            \`Généré le \${new Date().toLocaleString('fr-FR')}\`,
            pageWidth - 10,
            pageHeight - 6,
            { align: 'right' }
        );
        
        // Add small confidential notice
        doc.setFontSize(8);
        doc.text(
            '- Usage interne uniquement -',
            pageWidth - 10,
            pageHeight - 3,
            { align: 'right' }
        );
    }
    
    async function getEnhancedChartImage(selector) {
        return new Promise((resolve) => {
            const canvas = document.querySelector(selector);
            
            // Temporarily enhance chart for printing
            const originalBackground = canvas.style.background;
            canvas.style.background = 'white';
            
            html2canvas(canvas, {
                scale: 2, // Higher quality
                logging: false,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#FFFFFF'
            }).then(renderedCanvas => {
                canvas.style.background = originalBackground; // Restore original
                resolve(renderedCanvas.toDataURL('image/jpeg', 0.9));
            });
        });
    }
    
    function getChartDataValue(chartSelector, label) {
        const chart = Chart.getChart(document.querySelector(chartSelector));
        if (!chart) return 'N/A';
        
        const dataset = chart.data.datasets[0];
        const index = chart.data.labels.indexOf(label);
        return index !== -1 ? dataset.data[index] : 'N/A';
    }
    
    async function getTableData() {
        try {
            const headers = Array.from(document.querySelectorAll('#ticketTable thead th'))
                .map(th => th.textContent.trim());
            
            const rows = Array.from(document.querySelectorAll('#ticketTable tbody tr'))
                .map(tr => {
                    return Array.from(tr.querySelectorAll('td')).map(td => {
                        return td?.textContent?.trim() || '';
                    });
                })
                .filter(row => row.length > 0);
            
            return { headers, rows };
        } catch (error) {
            console.error('Erreur traitement tableau:', error);
            return { headers: [], rows: [] };
        }
    }
    
    function formatFrenchDate(monthString) {
        if (!monthString) return '';
        const [year, month] = monthString.split('-');
        const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                           'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
        return \`\${monthNames[parseInt(month) - 1]} \${year}\`;
    }
  </script>
</body>
</html>


`;