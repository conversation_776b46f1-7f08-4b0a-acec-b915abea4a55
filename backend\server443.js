const Suivi_Capteurs_html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <title>Suivi de capteur</title>
    <link id="favicon" rel="icon" type="image/x-icon" href="/assets/favicon.ico">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
 
    <style>
        body{
            background-image: url(https://www.meteo.tn/themes/custom/inm_theme/images/background.png);
            background-repeat: no-repeat;
            background-size: cover;
            display: flex;
            flex-direction: column;
            align-items: center;
            align-content: center;
        }
        /* General nav styles */
        #main{
            position: absolute;
            top: 15%;
            width: 95%;
            left: 4%;
        }

@keyframes blink {
    0%  { opacity: 1;}
    25% { opacity: 0.8;}
    50% { opacity: 0.5;}
    75% { opacity: 0.3; }
    100% { opacity: 0; }
}
/* Base navigation styles */
#navbar {
    background-color: #2c3e50;
    z-index: 990;
    width: 26px;
    height: 100vh;
    padding: 1.5rem 0;
    position: fixed;
    left: 0;
    top: 0;
    overflow: hidden;
    transition: width 0.8s;
    animation: blink 2s infinite;
}
#logout,#homepage{
    width: 100%;
    margin: 1rem 1rem 1rem 1rem;
    padding: 0.5rem;
    cursor: pointer;
    transition: width 0.8s;
   /* animation: blink 2s infinite;*/
}
#navbar:hover, #navbar:hover ul {
            width: 300px;
            background-color: #2c3e50;
            animation:  none;
        }
/* Navigation list container */
.navindex {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
    margin-left: -20px;
    top:10%;
    position: relative;
}

/* All list items in nav */
.navindex li {
    padding: 0.8rem 1.5rem;
    color: #ecf0f1;
    cursor: pointer;
    transition: all 0.8s ease;
    position: relative;
    
}

/* Main menu items */
.navindex > li{
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 4rem;
    z-index: 990;
}

/* Icons styling 
.navindex i {
    display: inline-flex;
    flex-wrap: nowrap;
    font-size: 1.2rem;
    text-align: center;
    align-items: center;
    gap: 0.5rem;
}
*/

/* Paragraph elements next to icons */
.navindex p {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 500;
}

/* Dropdown menu */

#dropdown-menu ul {
    list-style: none;
    padding: 0.5rem 0;
    margin: 0.5rem 0 0 2.2rem;
    display: none;
    background-color: #34495e;
    border-radius: 0.5rem;
    
}
#dropdown-menu1 ul {
    list-style: none;
    padding: 0.5rem 0;
    margin: 0.5rem 0 1 0.2rem;
    display: none;
    background-color: #34495e;
    border-radius: 0.5rem;
    
}

/* Show dropdown on hover */
#dropdown-menu:hover ul {
    display: block;
    z-index: 999;
}

#dropdown-menu1:hover ul {
    display: block;
    z-index: 999;
}

/* Dropdown items */
.dropdown-item {
    padding: 0.5rem 0.5rem !important;
    font-size: 0.9rem;
    margin-bottom: 0.2rem;
    z-index: 999;
    
}
.dropdown-item1 {
    padding: 0.5rem 0.5rem !important;
    font-size: 0.9rem;
    margin-bottom: 0.2rem;
    z-index: 999;
    
}

/* Hover effects */
.navindex > li:hover {
    background-color: #428bd4;
    border-radius: 0.5rem;
    transform: translateX(5px);
}

.dropdown-item:hover,.dropdown-item1:hover {
    background-color: #3498db !important;
}

/* Active state for primary button */
.btn-primary {
    background-color: #3498db;
}

/* Hover effect for links */
#historique_Capteur:hover,
#Aéroport_Capteur:hover {
    background-color: #428bd4;
    border-radius: 0.5rem;
    transform: translateX(5px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #navbar {
        min-width: 200px;
    }
    
    .navindex p {
        font-size: 0.9rem;
    }
    
    .navindex i {
        font-size: 1rem;
    }
}
        /* Individual Item Styles */

        .hidden{
            display: none;
        }
        .table{
            width: 100%;
        }
        .table td {
            border: 1px solid black;
            padding: 10px;
            text-align: center;
            align-content: center;
        }
        #search_result_head{
            position: sticky;
            top: 0;
            z-index: 800;
        }
        .table th{
            padding: 10px;
            text-align: center;
            align-content: center;
            background-color: #2c3e50;
            color: white;
            font-size: 1.3rem;
        }
        .equaltr, .equaltrr{
            width:100%;
            text-align: center;
        }
        .equaltr :nth-child(1){
            width: 50%;
        }
        .equaltrr{
            cursor: pointer;
        }
        .equaltrr :nth-child(odd) {
            width: 33.33%;
        }
        .input-group{
            width: 20%;
            text-align: center;
            align-items: center;
            align-content: center;
            left: 0%;
            margin-bottom: 2%;
        }
        .text-center{
            border: 1px solid black !important;
        }
        .ww{
            border: 1px solid black;
            border-top: 1px solid black !important;
        }
        .soustable2, .soustable3{
            width: 100%;
        }
        .vibrate-1{-webkit-animation:vibrate-1 .3s linear infinite both;animation:vibrate-1 .3s linear infinite both}
        @-webkit-keyframes vibrate-1{0%{-webkit-transform:translate(0);transform:translate(0)}20%{-webkit-transform:translate(-2px,2px);transform:translate(-2px,2px)}40%{-webkit-transform:translate(-2px,-2px);transform:translate(-2px,-2px)}60%{-webkit-transform:translate(2px,2px);transform:translate(2px,2px)}80%{-webkit-transform:translate(2px,-2px);transform:translate(2px,-2px)}100%{-webkit-transform:translate(0);transform:translate(0)}}@keyframes vibrate-1{0%{-webkit-transform:translate(0);transform:translate(0)}20%{-webkit-transform:translate(-2px,2px);transform:translate(-2px,2px)}40%{-webkit-transform:translate(-2px,-2px);transform:translate(-2px,-2px)}60%{-webkit-transform:translate(2px,2px);transform:translate(2px,2px)}80%{-webkit-transform:translate(2px,-2px);transform:translate(2px,-2px)}100%{-webkit-transform:translate(0);transform:translate(0)}}
        #inm {
                    width: 80%; /* Adjust the width as needed */
                    margin-bottom: 2%;
                }
        .logo{
            position: relative;
            left: 44%;
            margin-top: -50px;
        }
      
  .search-icon {
    position: absolute;
    right: 10px;  /* Adjust as needed for padding */
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;  /* Prevent SVG from capturing clicks */
    color: #6c757d;  /* Adjust the color if necessary */
  }
  #searchcapteur {
    padding-right: 36px;  /* Add padding to prevent text overlapping the icon */
  }
  input[type="checkbox"] {
    transform: scale(1.3);
    margin-top: 0.5rem;
}

#ajouterFiche{
    display:none; 
    margin-top: 20px;
    border:1px solid black;
    height:2rem;
    text-align:center;
}
.trp{
    cursor: pointer;
}

.redBlink {
  -webkit-animation: 1s blink ease infinite !important;
  -moz-animation: 1s blink ease infinite;
  -ms-animation: 1s blink ease infinite;
  -o-animation: 1s blink ease infinite;
  animation: 1s blink ease infinite !important;
  background-color: red !important; 
  color:yellow !important;
}
.blinkOrange{
    -webkit-animation: 1s blink ease infinite !important;
  -moz-animation: 1s blink ease infinite;
  -ms-animation: 1s blink ease infinite;
  -o-animation: 1s blink ease infinite;
  animation: 1s blink ease infinite !important;
  background-color: orange !important; 
  color:blue !important;
}

@keyframes blink {
  from, to {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

@-moz-keyframes blink {
  from, to {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

@-webkit-keyframes blink {
  from, to {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

@-ms-keyframes blink {
  from, to {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

@-o-keyframes blink {
  from, to {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}
.flexi{
    display :flex ;
    flex-direction: row;
    align-items : center;
    text-align : center;
    justify-content :space-evenly;
   /* margin-left :3%;*/
}
@media print {
    body {
      background-image: none !important;
    }
  }

</style>
</head>
<body>
    

    <header class="centered">
        <h1 id="title">Suivi des Capteurs</h1>
    </header>
    <div class="logo">
        <img id="inm" src="" alt="INM:img">
        <h6 class="vibrate-1" id="ipadr"></h6>
    </div>
    <nav id="navbar" class="nav flex-column">
        <ul class="navindex">
            <li id="dropdown-menu"><i class="bi bi-thermometer-half"></i> <p>Capteur</p>
                <ul>
                    <li id="Ajouter_Capteur" class="dropdown-item"><i class="bi bi-plus-circle"> Ajouter</i></li> 
                    <li id="Modifier_Capteur" class="dropdown-item"><i class="bi bi-pencil-square"> Modifier</i></li>
                    <li id="Supprimer_Capteur" class="dropdown-item"><i class="bi bi-trash3"> Supprimer</i></li>
                 </ul>
            </li>
            <li id="historique_Capteur">
                <i class="bi bi-file-text"></i><p>Historique Capteur</p>
            </li>
            <li id="Aéroport_Capteur">
                <i class="bi bi-airplane"></i><p>Fiche Aéroport</p>
            </li>
            <li id="Aéroport_Map">
                <i class="bi bi-geo-alt"></i><p>Map Aéroport</p>
            </li>

            <li id="dropdown-menu1"><i class="bi bi-gear"></i> <p>Maintenace</p>
                <ul>
                    <li id="Ajouter_Intervention" class="dropdown-item1"><i class="bi bi-plus-circle">Ajouter</i></li> 
                    <li id="Historique_Intervention" class="dropdown-item1"><i class="bi bi-file-text">Historique</i></li>
                 </ul>
            </li>
            <div class="d-flex flex-column gap-2 mt-2">
                <button id="homepage" onclick="window.location.href='/'" class="btn btn-success"><i class="bi bi-house-check-fill"></i>&nbsp; Accueil</button>
                <button id="logout" onclick="window.location.href='/logout'" class="btn btn-danger"><i class="bi bi-box-arrow-left"></i>&nbsp; Déconnexion</button>
            </div>
        </ul>
    </nav>
    <div id="main" class="main">
        <div id="searchcapteurss" class="input-group mb-3" style="position: relative;">
            <input id="searchcapteur" type="text" class="form-control" placeholder="Inserez N° Capteur" aria-label="Capteur number">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-search search-icon" viewBox="0 0 16 16">
              <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398q.044.06.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1 1 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0"/>
            </svg>
          </div>
          
        
        <div id="search_result" class="table-active">
            <table class="table table-hover table-striped-columns table-bordered">
                <thead id="search_result_head">
                    <tr>
                        <th class="ww">Type Capteur</th>
                        <th class="ww">N° Capteur</th>
                        <th class="ww">Emplacement</th>
                        <th class="ww">
                            <table class="w-100">
                                <tr>
                                    <th colspan="3" class="text-center">Étalonage</th>
                                </tr>
                                <tr class="equaltrr">
                                    <th>Date Début</th>
                                    <th>Date Fin</th>
                                    <th>Étaloné Par</th>
                                </tr>
                            </table>
                        </th>
                        <th class="ww">
                            <table class="w-100">
                                <tr>
                                    <th colspan="2" class="text-center">Installation</th>
                                </tr>
                                <tr class="equaltr">
                                    <th>Date</th>
                                    <th>Technicien</th>
                                </tr>
                            </table>
                        </th>
                    </tr>
                </thead>
                <tbody id="search_result_body">
     
                </tbody>
            </table>



        </div>
    </div>

  




    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="exampleModalLabel"></h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div id="modalBody" class="modal-body">
                ...
            </div>
            <div class="modal-footer">
                <button id="SUBMIT_IT" type="submit" class="btn btn-primary">Enregistrer</button>
            </div>
            </div>
        </div>
        </div>



<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.14.7/dist/umd/popper.min.js" integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
 
<script>
document.addEventListener("keydown", e => {
    if (e.ctrlKey && e.key === "u") {
      alert("❌ C'est interdit de faire ça !");
      e.preventDefault();
    }
  });

    let AirportData = []
    const types = //['HUMIDITE','TEMPERATURE','RAYONNEMENT','DIRECTION','VITESSE','PRESSION']
    [
        {
            "serial":'HUMIDITE',
            "id":0
        },
        {
            "serial":'TEMPERATURE',
            "id":1
        },
        {
            "serial":'RAYONNEMENT',
            "id":2
        },
        {
            "serial":'DIRECTION',
            "id":3
        },
        {
            "serial":'VITESSE',
            "id":4
        },
        {
            "serial":'PRESSION',
            "id":5
        }
    ]
    let capteurs = 'hello',emplacements=[],zones=[];
    const Sessiondata = JSON.parse(sessionStorage.getItem("sessiondata"))
    let lastmodify = ''
    let lasttype=''
    let Allprojects;
    const host = "";// 'http://*************'
    document.getElementById("ipadr").textContent =  Sessiondata.ip;
    document.getElementById("inm").src = Sessiondata.image;
    window.onload = async function () {
        try {
            await getCapteurs();               // Waits until finished
            await populatetable();            // Then runs this
            await getAndClassifyProjects(); // Then this
            console.log('All functions completed.');
            // You can use classifiedProjects here if needed
        } catch (err) {
            console.error('Error in running sequence:', err);
        }
    }
   // runAllInSequence();

    document.getElementById("SUBMIT_IT").addEventListener('click',SendForm)
    const Capteur = document.getElementById("dropdown-menu")
    const ajoutCapteur = document.getElementById("Ajouter_Capteur")
    const modifCapteur = document.getElementById("Modifier_Capteur")
    const supprCapteur = document.getElementById("Supprimer_Capteur")
    const historCapteur = document.getElementById("historique_Capteur")
    const aeroportCapteur = document.getElementById("Aéroport_Capteur")
    const Ajouter_Intervention = document.getElementById("Ajouter_Intervention") 
    const Aeroport_Map = document.getElementById("Aéroport_Map")
    const Historique_Intervention = document.getElementById("Historique_Intervention")

    Aeroport_Map.addEventListener("click",()=>{
        window.location.href = "/map"
    })
    Capteur.addEventListener("click",()=>{
        document.getElementById("printBtn")?.remove();
        populatetable();
    });
    ajoutCapteur.addEventListener("click",()=>{
        document.getElementById("printBtn")?.remove();
        createModal("ajoutCapteur")
    });
    modifCapteur.addEventListener("click",()=>{
        document.getElementById("printBtn")?.remove();
        createModal("modifCapteur")
    });
    supprCapteur.addEventListener("click",()=>{
        document.getElementById("printBtn")?.remove();
        createModal("supprCapteur")
    });
    historCapteur.addEventListener("click",()=>{
        document.getElementById("printBtn")?.remove();
        createModal("HistoryCapteur")
        
    })
    aeroportCapteur.addEventListener("click",()=>{
        document.getElementById("printBtn")?.remove();
        createModal("airports")
    })
    Ajouter_Intervention.addEventListener("click",()=>{
        document.getElementById("printBtn")?.remove();
        createModal("addIntervention")
    })
    Historique_Intervention.addEventListener("click",()=>{
        document.getElementById("printBtn")?.remove();
        createModal("getInterventions")
    })

    async function createModal(demand){
        if(demand === "ajoutCapteur"){
            document.getElementById("exampleModalLabel").innerHTML = "Ajouter Capteur"
            document.getElementById("modalBody").innerHTML = ModalajoutCapteur;
            document.getElementById("SUBMIT_IT").innerText = "Ajouter"
            var paramModal = new bootstrap.Modal(document.getElementById("exampleModal"));
            paramModal.show();
            Populate_datalists(types,'types')
            Populate_datalists(emplacements,'emplacementList')
            Populate_datalists(zones,'zoneList')
        }
        if(demand === "modifCapteur"){
            lasttype=''
            document.getElementById("exampleModalLabel").innerHTML = "Modifier Capteur"
            document.getElementById("modalBody").innerHTML = ModalmodifCapteur;
            document.getElementById("SUBMIT_IT").innerText = "Modifier"
            var paramModal = new bootstrap.Modal(document.getElementById("exampleModal"));
            paramModal.show();
           // const capteurs = ['Capteur 001', 'Capteur 002', 'Capteur 003']
           Populate_datalists(capteurs,'capteurList')
           Populate_datalists(emplacements,'emplacementList')
           Populate_datalists(zones,'zoneList')
           document.getElementById("maininput").addEventListener('input', function(){
            const inputValue = this.value;
            //console.log("this.value = ",inputValue)
            let found = false;
            capteurs.forEach(item => {
                if(inputValue == item.serial){
                    //console.log(item)
                    found = true;
                    document.getElementById("type").value = item.type
                    lasttype = item.type
                    document.getElementById("input1").value = item.emplacement
                    document.getElementById("zone").value = item.zone
                    document.getElementById("input2").value = item.etalonnage_date_debut.split('T')[0]
                    lastmodify = item.etalonnage_date_debut.split('T')[0]
                    document.getElementById("input3").value = item.etalonnage_date_fin.split('T')[0]
                    document.getElementById("input4").value = item.etalonnage_etalone_par
                    document.getElementById("input5").value = item.installation_date.split('T')[0]
                    document.getElementById("input6").value = item.installation_technicien
                    document.getElementById("input2").addEventListener('input',checkmodifydate)
                    document.getElementById("ajouterFiche").style.display = 'block'
                    document.getElementById("fileinsert").addEventListener('change', (event) => { fileinsert
                        if (event.target.checked) {
                            checkmodifydate(1);  // Checkbox is checked
                        }
                    })
                    return;
                }
                else{if(found)return
                    document.getElementById("input1").value = ''
                    document.getElementById("zone").value = ''
                    document.getElementById("input2").value = ''
                    document.getElementById("input3").value = ''
                    document.getElementById("input4").value = ''
                    document.getElementById("input5").value = ''
                    document.getElementById("input6").value = ''
                    document.getElementById("ajouterFiche").style.display = 'none'
                }
            })
           })
        }
        if(demand === "supprCapteur"){
            document.getElementById("exampleModalLabel").innerHTML = "Supprimer Capteur"
            document.getElementById("modalBody").innerHTML = ModalsupprCapteur;
            document.getElementById("SUBMIT_IT").innerText = "Supprimer definitivement"
            var paramModal = new bootstrap.Modal(document.getElementById("exampleModal"));
            paramModal.show();
           // const capteurs = ['Capteur 001', 'Capteur 002', 'Capteur 003']
            Populate_datalists(capteurs,'suppcapteurList')
        }
        if(demand === "HistoryCapteur"){
            document.getElementById("exampleModalLabel").innerHTML = "Historique Capteur"
            document.getElementById("modalBody").innerHTML = ModalhystoryCapteur;
            document.getElementById("SUBMIT_IT").innerText = "Recherche"
            var paramModal = new bootstrap.Modal(document.getElementById("exampleModal"));
            paramModal.show();
           // const capteurs = ['Capteur 001', 'Capteur 002', 'Capteur 003']
            Populate_datalists(capteurs,'suppcapteurList')
        }
        if(demand === "airports"){
            document.getElementById("exampleModalLabel").innerHTML = "Select Aéroport"
            document.getElementById("modalBody").innerHTML = aeroportList;
            document.getElementById("SUBMIT_IT").innerText = "Select"
            var paramModal = new bootstrap.Modal(document.getElementById("exampleModal"));
            paramModal.show();
           // const capteurs = ['Capteur 001', 'Capteur 002', 'Capteur 003']
            Populate_datalists(capteurs,'aeroportList')
        }
        if(demand === "addIntervention"){
            document.getElementById("exampleModalLabel").innerHTML = "Ajouter Intervention"
            document.getElementById("modalBody").innerHTML = ModaladdIntervention;
            document.getElementById("SUBMIT_IT").innerText = "Ajouter"
            var paramModal = new bootstrap.Modal(document.getElementById("exampleModal"));
            paramModal.show();
          
           // const capteurs = ['Capteur 001', 'Capteur 002', 'Capteur 003']
            Populate_datalists(capteurs,'capteurList')
            Populate_datalists(Allprojects,'projectList')
            
        }
        if(demand === "getInterventions"){
            document.getElementById("exampleModalLabel").innerHTML = "Historique Intervention"
            document.getElementById("modalBody").innerHTML = ModalgetInterventions;
            document.getElementById("SUBMIT_IT").innerText = "Recherche"
            var paramModal = new bootstrap.Modal(document.getElementById("exampleModal"));
            paramModal.show();
           // const capteurs = ['Capteur 001', 'Capteur 002', 'Capteur 003']
            Populate_datalists(capteurs,'capteurList')
        }
    }
    const aeroportList = \`
    <form id="aeroport">
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Aéroports List</label>
                    <input class="form-control" list="aeroportList" name="airport" type="text" placeholder="Aéroport" autocomplete="off" required>
                    <datalist id="aeroportList">
                    </datalist>
                </form>
    \`;
    const ModalhystoryCapteur = \`
                <form id="historyform">
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Numéro de Serie Capteur</label>
                    <input class="form-control" list="suppcapteurList" name="serial" type="text" placeholder="N° Capteur" autocomplete="off" required>
                    <datalist id="suppcapteurList">
                    </datalist>
                </form>
    \`;
    const ModalsupprCapteur = \`
                <form id="suppform">
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Numéro de Serie Capteur</label>
                    <input class="form-control" list="suppcapteurList" name="serial" type="text" placeholder="N° Capteur" autocomplete="off" required>
                    <datalist id="suppcapteurList">
                    </datalist>
                </form>
    \`
    const ModalmodifCapteur = \`
                <form id="modifform">
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Numéro de Serie Capteur</label>
                    <input id="maininput" class="form-control" name="serial" list="capteurList" type="text" placeholder="N° Capteur" autocomplete="off" required>
                    <datalist id="capteurList">
                    </datalist>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Type Capteur</label>
                    <input class="form-control" id="type" name="type" list="types" type="text" placeholder="select type" autocomplete="off" required>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Emplacement</label>
                    <input list="emplacementList" id="input1" class="form-control" name="emplacement" type="text" placeholder="Emplacement Capteur" autocomplete="off" required>
                    <datalist id="emplacementList">
                    </datalist>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">zone / site </label>
                    <input id="zone" list="zoneList" class="form-control" name="zone" type="text" placeholder="zone Capteur" autocomplete="off" required>
                    <datalist id="zoneList">
                    </datalist>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Date étalonage </label>
                    <input id="input2" class="form-control" name="etalonnage_date_debut" type="date" placeholder="Date étalonage Capteur" required>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Date Fin étalonage</label>
                    <input id="input3" class="form-control" name="etalonnage_date_fin" type="date" placeholder="Fin étalonage Capteur" required>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">étaloné Par Monsieur</label>
                    <input id="input4" class="form-control" name="etalonnage_etalone_par" type="text" placeholder="Monsieur" required>
                    <div id="ajouterFiche" style="display:none; margin-top:20px">
                    <input id="fileinsert" name="fileinsert" type="checkbox">
                    <label style="color:blue;">&nbsp;Ajouter Fiche d'étalonage ?</label>
                    </div>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Date Installation Capteur</label>
                    <input id="input5" class="form-control" name="installation_date" type="date" placeholder="Installation Capteur" required>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Technicien d'installation</label>
                    <input id="input6" class="form-control" name="installation_technicien" type="text" placeholder="Technicien" required>
                </form>
    \`
    const ModalajoutCapteur = \`
       
                <form id="ajoutform">
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Type Capteur</label>
                    <input class="form-control" name="type" list="types" type="text" placeholder="select type" autocomplete="off" required>
                    <datalist id="types">
                    </datalist>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Numéro de Serie Capteur</label>
                    <input class="form-control" name="serial" type="text" placeholder="N° Capteur" autocomplete="off" required>
                    <span id="capteurexist" style="color:red;display:none;"></span>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Emplacement</label>
                    <input list="emplacementList" class="form-control" name="emplacement" type="text" placeholder="Emplacement Capteur" autocomplete="off" required>
                    <datalist id="emplacementList">
                    </datalist>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">zone / site </label>
                    <input list="zoneList" class="form-control" name="zone" type="text" placeholder="zone Capteur" autocomplete="off" required>
                    <datalist id="zoneList">
                    </datalist>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Date étalonage </label>
                    <input class="form-control" name="etalonnage_date_debut" type="date" placeholder="Date étalonage Capteur" required>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Date Fin étalonage</label>
                    <input class="form-control" name="etalonnage_date_fin" type="date" placeholder="Fin étalonage Capteur" required>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">étaloné Par Monsieur</label>
                    <input class="form-control" name="etalonnage_etalone_par" type="text" placeholder="Monsieur" required>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Fiche d'etalonage</label>
                    <input class="form-control" name="fiche" type="file" accept=".pdf" required>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Date Installation Capteur</label>
                    <input class="form-control" name="installation_date" type="date" placeholder="Installation Capteur" required>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Technicien d'installation</label>
                    <input class="form-control" name="installation_technicien" type="text" placeholder="Technicien" required>
                </form>

    \`;
    const ModaladdIntervention= \`
    <form id="addIntervention">
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Numéro de Projet</label>
                    <input class="form-control" list="projectList" name="project" type="text" placeholder="id de Projet" autocomplete="off"required>
                    <datalist id="projectList">
                    </datalist>

                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Numéro de Serie Capteur</label>
                    <input class="form-control" list="capteurList" name="serial" type="text" placeholder="N° Capteur" autocomplete="off">
                    <datalist id="capteurList">
                    </datalist>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Emplacemet / ville</label>
                    <input class="form-control" name="emplacement" type="text" placeholder="ville ou aeroport" required>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Date Intervention</label>
                    <input class="form-control" name="date" type="date" required>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Technicien d'intervention</label>
                    <input class="form-control" name="technicien" type="text" placeholder="Technicien" required>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Type Intervention</label>
                    <input class="form-control" name="type" type="text" placeholder="Type Intervention" required>
                    <label style="margin-bottom:0.1rem;margin-top:0.6rem">Description Intervention</label>
                    <textarea class="form-control" style="height: 160px;" name="description" type="text" placeholder="Description Intervention" required></textarea>
                </form>
    \`;
    const ModalgetInterventions= \`
                    <form id="getInterventions">
                    <strong style="margin-left: 26%;font-size: 1.7rem;color: red;margin-bottom:0.1rem;margin-top:0.6rem">Rechercher par</strong></br>
                    <label for='serial' style="margin-bottom:0.1rem;margin-top:0.6rem">Numéro de Serie Capteur</label>
                    <input class="form-control" list="capteurList" name="serial" type="text" placeholder="N° Capteur" autocomplete="off">
                    <datalist id="capteurList">
                    </datalist>
                    <strong style="margin-left:40%;margin-bottom:0.6rem;margin-top:0.6rem">- OU -</strong></br>
                    <label for='emplacement' style="margin-bottom:0.1rem;margin-top:0.6rem">Emplacement</label>
                    <input class="form-control" name="emplacement" type="text" placeholder="ville ou aeroport">

                    <strong style="margin-left:40%;margin-bottom:0.6rem;margin-top:0.6rem">- OU -</strong></br>
                    <label for='DATE' style="margin-bottom:0.1rem;margin-top:0.6rem">Date Intervention</label>
                    <input class="form-control" name="DATE" type="date">

                </form>
 \`;

async function getCapteurs(){
    await fetch(host+"/api/probes")
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok ' + response.statusText);
            }
            return response.json(); // Properly parse the JSON response
        })
        .then(Data => {
            //console.log("data = ", Data);
            capteurs =  Data.data
            capteurs.forEach(item => {
                if(!emplacements.includes(item.emplacement)){emplacements.push(item.emplacement)}
                if(!zones.includes(item.zone)){zones.push(item.zone)}
            })
            if(!emplacements.includes('Laboratoire')){emplacements.push('Laboratoire')}
            if(!zones.includes('Labo')){zones.push('Labo')}
        })
        .catch(error => {
            console.error('There was a problem with the fetch operation:', error);
            
        });
}

function populatetableIntervention(data,entry){
    const thead = document.getElementById('search_result_head');
    const tbody = document.getElementById('search_result_body');
    thead.innerHTML = '';
    tbody.innerHTML = '';
    const headers = [
            'Date Intervention',
            'Technicien',
            'Type',
            'Description'
        ];

        const trHead = document.createElement('tr');
        headers.forEach((header) => {
            const th = document.createElement('th');
            th.textContent = header;
            if(header == 'Description') th.style.width = '70%'
            trHead.appendChild(th);
        });
        thead.appendChild(trHead);

        // Populate table rows with data
        const DATA = data.data
        
                // Create table body
                DATA.forEach(item => {
                const row = document.createElement('tr');
                //document.getElementById("title").innerHTML = \`\${entry}\`
                headers.forEach(header => {
                    const cell = document.createElement('td');
                    switch (header) {
                        case 'Date Intervention':
                            cell.textContent = new Date(item.date_time).toLocaleDateString();
                            break;
                        case 'Technicien':
                            cell.textContent = item.technicien || 'N/A';
                            break;
                        case 'Type':
                            cell.textContent = item.type || 'N/A';
                            break;
                        case 'Description':
                            cell.textContent = item.intervention || 'N/A';
                            break;
                        default:
                            cell.textContent = 'N/A';
                    }
                    row.appendChild(cell);
                });

                tbody.appendChild(row);
            });
     
}

function populatetableHystory(data){
    const thead = document.getElementById('search_result_head');
    const tbody = document.getElementById('search_result_body');
    thead.innerHTML = '';
    tbody.innerHTML = '';
    const headers = [
            'Emplacement',
            'Zone',
        // 'Probe ID',
            'Etalonné Par',
            'Etalonnage Date',
            'Fin étalonnage Date',
            'Installation Date',
            'Installation Technicien'
        ];

        const trHead = document.createElement('tr');
        headers.forEach((header) => {
            const th = document.createElement('th');
            th.textContent = header;
            trHead.appendChild(th);
        });
        thead.appendChild(trHead);

        // Populate table rows with data
        //const DATA = data.data
        
                // Create table body
                data.forEach(item => {
                const row = document.createElement('tr');
                document.getElementById("title").innerHTML = \`<strong><u>N° Serie</u>:</strong>&nbsp;&nbsp; \${item.serial}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong><u>Type</u>:</strong>&nbsp;&nbsp; \${item.type}\`;
                headers.forEach(header => {
                    const cell = document.createElement('td');
                    switch (header) {
                        case 'Emplacement':
                            cell.textContent = item.emplacement;
                            break;
                        case 'Zone':
                            cell.textContent = item.zone;
                            break;
                        case 'Etalonné Par':
                            cell.textContent = item.etalonnage_etalone_par || 'N/A';
                            break;
                        case 'Etalonnage Date':
                            cell.textContent = convertToLocalDate(item.etalonnage_date_debut);
                            break;
                        case 'Fin étalonnage Date':
                            cell.textContent = convertToLocalDate(item.etalonnage_date_fin);
                            break;
                        case 'Installation Date':
                            cell.textContent = convertToLocalDate(item.installation_date);
                            break;
                        case 'Installation Technicien':
                            cell.textContent = item.installation_technicien || 'N/A';
                            break;
                        default:
                            cell.textContent = 'N/A';
                    }
                    row.appendChild(cell);
                });

                tbody.appendChild(row);
            });

            //table.appendChild(tbody);         
}

async function populatetable(data,aeroport){
    const thead = document.getElementById('search_result_head');
    const tbody = document.getElementById('search_result_body');
    const headers = [
            'Type',
            'N° Serie',
            'Zone',
        // 'Probe ID',
            'Etalonné Par',
            'Etalonnage Date',
            'Fin étalonnage Date',
            'Installation Date',
            'Installation Technicien'
        ];

    if(data){
        if(aeroport == 'hystory'){
            populatetableHystory(data.data)
            return
        }
        //console.log(data); 
        document.getElementById("title").textContent = aeroport
        // Create the "imprimer" button
        const printButton = document.createElement("button");
        printButton.textContent = "imprimer";
        printButton.id = "printBtn";
        printButton.className = "btn btn-outline-danger";
        printButton.style.marginLeft = "25%";
        printButton.onclick = () => {
            document.getElementById("printBtn").style.display = 'none'; // Hide the button before printing
            document.getElementById("navbar").style.display = 'none'; // Hide the title before printing
            document.getElementById("searchcapteurss").style.display = 'none'; // Show the table before printing  
            document.getElementById("ipadr").style.display = 'none';
            document.getElementById("search_result_head").querySelectorAll('th').forEach(th => {
                th.style.backgroundColor = 'black';
              });
            
            window.print();
        } // Optional: triggers browser print dialog
        // Restore elements after printing
window.onafterprint = () => {
    document.getElementById("printBtn").style.display = '';
    document.getElementById("navbar").style.display = '';
    document.getElementById("searchcapteurss").style.display = '';
    document.getElementById("ipadr").style.display = '';
    document.getElementById("search_result_head").querySelectorAll('th').forEach(th => {
        th.style.backgroundColor = '#2c3e50';
      });
};
        
        // Append the button after the title element
        document.getElementById("title").after(printButton);
        // Clear previous table content
        thead.innerHTML = '';
        tbody.innerHTML = '';
  // Create table headers

        const trHead = document.createElement('tr');
        headers.forEach((header) => {
            const th = document.createElement('th');
            th.textContent = header;
            trHead.appendChild(th);
        });
        thead.appendChild(trHead);

        // Populate table rows with data
        const DATA = data.data

        const zonePriority = {
            "centrale": 1,
            "Principale": 2,
            "secour": 3,
            "Parc N":4,
            "Parc S":5
        };

        // Sort the DATA array based on zone priority
        DATA.sort((a, b) => {
            const priorityA = zonePriority[a.zone] || 999; // Default to 999 for other zones
            const priorityB = zonePriority[b.zone] || 999;
            return priorityA - priorityB;
        });

        const DateTime = new Date();
        let lastserial,lastetalonagedate,lastfinetalonage,lastinstallation,last=false;
        DATA.forEach((zoneData) => {
            const { zone, probes } = zoneData;
            probes.forEach((probe) => {
            const tr = document.createElement('tr');
            tr.className = 'trp'
            tr.id = probe.probe_id

            const typeCell = document.createElement('td');
            const Contentt = document.createElement('div');
            const TYPE = document.createElement('p');
            TYPE.textContent = probe.type || '';
            const IMAGE = document.createElement('i');
            if(probe.type == 'HUMIDITE') IMAGE.className = 'bi bi-droplet-fill';
            else if(probe.type == 'TEMPERATURE') IMAGE.className = 'bi bi-thermometer-half';
            else if(probe.type == 'RAYONNEMENT') IMAGE.className = 'bi bi-brightness-high';
            else if(probe.type == 'DIRECTION') IMAGE.className = 'bi bi-compass';
            else if(probe.type == 'VITESSE') IMAGE.className = 'bi bi-wind';
            else if(probe.type == 'PRESSION') IMAGE.className = 'bi bi-chevron-bar-contract';
            Contentt.style.display = 'flex';
            Contentt.style.flexDirection = 'row';
            Contentt.style.alignItems = 'center';
            Contentt.style.textAlign = 'center';
            Contentt.style.justifyContent = 'space-between';
            Contentt.style.marginLeft = '20%;';
            TYPE.style.margin = '0';
            IMAGE.style.transform = 'scale(1.7)';
            Contentt.appendChild(TYPE);
            Contentt.appendChild(IMAGE);
            
            
            typeCell.appendChild(Contentt);
            //typeCell.textContent = probe.type || '';
            tr.appendChild(typeCell);

            const thisDate = new Date(probe.etalonnage_date_fin);
            const cellClass = ComapareDates(DateTime, thisDate);

            const serialCell = document.createElement('td');
            serialCell.textContent = probe.serial || '';
            tr.appendChild(serialCell);
 if(lastserial == probe.serial)last = true   //if(!(lastserial == probe.serial)){
            lastserial=probe.serial

            const zoneCell = document.createElement('td');
            zoneCell.textContent = zone;
            tr.appendChild(zoneCell);

            const etaloneParCell = document.createElement('td');
            etaloneParCell.textContent = probe.etalone_par || '';
            tr.appendChild(etaloneParCell);

            const etalonnageStartDateCell = document.createElement('td');
            last==true? etalonnageStartDateCell.textContent = lastetalonagedate :etalonnageStartDateCell.textContent = convertToLocalDate(probe.etalonnage_date_debut) || '';
            tr.appendChild(etalonnageStartDateCell);
            lastetalonagedate = etalonnageStartDateCell.textContent

            const etalonnageEndDateCell = document.createElement('td');
            etalonnageEndDateCell.className = cellClass;
            last==true? etalonnageEndDateCell.textContent = lastfinetalonage :etalonnageEndDateCell.textContent = convertToLocalDate(probe.etalonnage_date_fin) || '';
            tr.appendChild(etalonnageEndDateCell);
            lastfinetalonage=etalonnageEndDateCell.textContent

            const installationDateCell = document.createElement('td');
            last==true? installationDateCell.textContent = lastinstallation :installationDateCell.textContent = convertToLocalDate(probe.installation_date) || '';
            tr.appendChild(installationDateCell);
            lastinstallation=installationDateCell.textContent

            const installationTechnicienCell = document.createElement('td');
            installationTechnicienCell.textContent = probe.installation_technicien || '';
            tr.appendChild(installationTechnicienCell);

            tr.addEventListener('click', () => handleRowClick(probe.serial, etalonnageStartDateCell.textContent));
            
            tbody.appendChild(tr);

            last = false
            });
        });

                return
                }
            
    else{
                document.getElementById("title").textContent = "Suivi des Capteurs"
                //await getCapteurs()
                    //console.log("-----",capteurs)
                    thead.innerHTML = '';
                    tbody.innerHTML = '';
                    createTableHeader(headers);
                    let i=0,lastzone=''
                
                const DateTime = new Date();

                capteurs.forEach(item => {
                    
                    if(!(item.emplacement === lastzone) ) {
                        const trZone = document.createElement('tr');
                        trZone.className = 'aeroportsNames'
                        const tdZone = document.createElement('td');
                        tdZone.colSpan = headers.length;
                        tdZone.textContent = item.emplacement;
                        tdZone.style.backgroundColor = 'black';
                        tdZone.style.color = 'white';
                        tdZone.style.fontSize = '2rem';
                        trZone.appendChild(tdZone);
                        tbody.appendChild(trZone);
                    };

                    const Contentt = document.createElement('div');
                    const TYPE = document.createElement('p');
                    TYPE.textContent = item.type || '';

                    const IMAGE = document.createElement('i');
                    if (item.type == 'HUMIDITE') IMAGE.className = 'bi bi-droplet-fill';
                    else if (item.type == 'TEMPERATURE') IMAGE.className = 'bi bi-thermometer-half';
                    else if (item.type == 'RAYONNEMENT') IMAGE.className = 'bi bi-brightness-high';
                    else if (item.type == 'DIRECTION') IMAGE.className = 'bi bi-compass';
                    else if (item.type == 'VITESSE') IMAGE.className = 'bi bi-wind';
                    else if (item.type == 'PRESSION') IMAGE.className = 'bi bi-chevron-bar-contract';

                    Contentt.style.display = 'flex';
                    Contentt.style.flexDirection = 'row';
                    Contentt.style.alignItems = 'center';
                    Contentt.style.textAlign = 'center';
                    Contentt.style.justifyContent = 'space-between';
                    Contentt.style.marginLeft = '20%';
                    TYPE.style.margin = '0';
                    IMAGE.style.transform = 'scale(1.7)';

                    Contentt.appendChild(TYPE);
                    Contentt.appendChild(IMAGE);

                    const thisDate = new Date(item.etalonnage_date_fin);
                    const cellClass = ComapareDates(DateTime, thisDate);

                    const row = document.createElement('tr');
                    row.addEventListener('click', () => handleRowClick(item.serial, item.etalonnage_date_debut));

                    // Create a separate <td> for Contentt and append it manually
                    const contentCell = document.createElement('td');
                    contentCell.appendChild(Contentt);
                    row.appendChild(contentCell);
                    row.innerHTML += \`
                        <td>\${item.serial}</td>
                        <td><div class="flexi">\${item.emplacement}<p>&nbsp;&nbsp;</p>\${item.zone}</div></td>

                        <td>
                        <table class="soustable3">
                            <tr class="equaltrr">
                            <td>\${convertToLocalDate(item.etalonnage_date_debut)}</td>
                            <td class=\${cellClass}>\${convertToLocalDate(item.etalonnage_date_fin)}</td>
                            <td>\${item.etalonnage_etalone_par}</td>
                            </tr>
                        </table>
                        </td>

                        <td>
                        <table class="soustable2">
                            <tr class="equaltr">
                                <td>\${convertToLocalDate(item.installation_date)}</td>
                                <td>\${item.installation_technicien}</td>
                            </tr>
                        </table>
                        </td>
                    \`;

                        // Append the row to the table body
                        tbody.appendChild(row);

                    //console.log(item.emplacement,{i, lastzone})

                lastzone = item.emplacement
                    });
                    const Aeroports = JSON.parse(sessionStorage.getItem("AirportData"))
                    if(!Aeroports){
                        await getAllAeroprt()
                        sessionStorage.setItem("AirportData", JSON.stringify(AirportData));
                    }
                }
    }
    function convertToLocalDate(utcDate) {
    const date = new Date(utcDate);
    date.setMinutes(date.getMinutes() - date.getTimezoneOffset()); // Adjust for timezone
    return date.toISOString().split('T')[0]; // Extract YYYY-MM-DD
    }

    function ComapareDates(d1, d2) {
    let date1 = new Date(d1).getTime();
    let date2 = new Date(d2).getTime();
    let now = Date.now(); // Current date in milliseconds
    let thirtyDaysFromNow = now + 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds

    // Check if d2 is in the past
    if (date2 < now) {
        return "redBlink";
    }

    // Check if d2 is within the next 30 days
    if (date2 >= now && date2 <= thirtyDaysFromNow) {
        return "blinkOrange";
    }

    // Compare d1 and d2 as fallback logic
    if (date1 < date2) {
        return "";
    } else if (date1 > date2) {
        return "redBlink";
    } else {
        return "redBlink";
    }
    }

    function handleRowClick(serial, etalonnageDateDebut) {
        let date = convertToLocalDate(etalonnageDateDebut); 
        let formattedDate = date.replace(/-/g, ""); 
    //  let formattedDate = etalonnageDateDebut.replace(/-/g, ""); 
    // Define the endpoint and query parameters
    const url = \`/get-pdf?serial=\${encodeURIComponent(serial)}&date=\${encodeURIComponent(formattedDate)}\`;

        fetch(host+url)
        .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();  // Retrieve the response as a blob
        })
        .then(blob => {
        const pdfUrl = URL.createObjectURL(blob);  // Create a local URL for the PDF blob
        window.open(pdfUrl, '_blank');             // Open the PDF in a new tab
        URL.revokeObjectURL(pdfUrl);               // Clean up the object URL when done
        })
        .catch(error => {
        console.error('Error fetching PDF:', error);
        alert(\`❌ PDF de fiche étalonage n'existe pas.\`);
        });
    }

    document.getElementById('searchcapteur').addEventListener('input', function () {
    const filter = this.value.toLowerCase();
    const rows = document.querySelectorAll('#search_result_body tr');
        //console.log(rows)
    rows.forEach(row => {
        const isMainRow = !row.classList.contains('equaltrr') && !row.classList.contains('equaltr') && !row.classList.contains('aeroportsNames');
        const capteurNumber = isMainRow ? row.cells[1].textContent.toLowerCase() : ''; // Only check main rows
        
        if (isMainRow && capteurNumber.includes(filter)) {
        row.style.display = ''; // Show main row
        // Show related rows immediately following the main row
        let nextRow = row.nextElementSibling;
        while (nextRow && (nextRow.classList.contains('equaltrr') || nextRow.classList.contains('equaltr') || nextRow.classList.contains('aeroportsNames'))) {
            nextRow.style.display = ''; // Show related row
            nextRow = nextRow.nextElementSibling;
        }
        } else if (isMainRow) {
        row.style.display = 'none'; // Hide main row
        // Hide related rows immediately following the main row
        let nextRow = row.nextElementSibling;
        while (nextRow && (nextRow.classList.contains('equaltrr') || nextRow.classList.contains('equaltr') || nextRow.classList.contains('aeroportsNames'))) {
            nextRow.style.display = 'none'; // Hide related row
            nextRow = nextRow.nextElementSibling;
        }
        }
    });
    });

    function Populate_datalists(items,datalistID){
        const datalist = document.getElementById(datalistID);
        try{
            if(datalistID === "aeroportList"){
                let last
                items.forEach(item => {
                    if( last != item.emplacement){
                            const option = document.createElement('option');
                            option.value = item.emplacement;
                            option.id = item.id || null;
                            datalist.appendChild(option);
                            last = item.emplacement
                        }
                });
            }
            else if(datalistID === 'projectList'){
                items = items.nonconfirmed
                items.forEach(item => {
                    let parsedData = {},Projectnumber;
                    try {
                        parsedData = JSON.parse(item.data_);
                        Projectnumber = parsedData.projetnumber || null;
                    } catch (err) {
                        console.warn('Invalid JSON in data_ field:', item.data_);
                    }
                            const option = document.createElement('option');
                            option.value = item.id + ' : '+ Projectnumber;
                            option.id = item.id || null;
                            datalist.appendChild(option);
                });
            }
            else{
                items.forEach(item => {
                            const option = document.createElement('option');
                            option.value = item.serial || item;
                            option.id = item.id || null;
                            datalist.appendChild(option);
                        });
            }
            }
            catch(error){
                console.log(error)
            }
    }

    async function SendForm() {
        let forme = '';
        let methode = '',doneSerial=false,doneEmplacement=false,doneDate=false;
        let alertText,terminison = \`/api/probes\`,entry,tolabo=false,lasttypechange=false ;
        
        if (document.getElementById("historyform")) {
            forme = "historyform";
            methode = 'GEThistory';
            //alertText = ' Capteur Supprimer ✅'
        }
        else if (document.getElementById("suppform")) {
            forme = "suppform";
            methode = 'DELETE';
            alertText = ' Capteur Supprimer ✅'
        }
        else if (document.getElementById("modifform")) {
            forme = "modifform";
            methode = 'PUT';
            alertText = ' Capteur Modifier ✅'
            if(document.getElementsByName('emplacement')[0].value === 'Laboratoire'){
                document.getElementsByName('zone')[0].value = 'Labo'
                tolabo = true
            }
            if(document.getElementsByName('type')[0].value != lasttype){
                lasttypechange=true;
            }
        }
        else if(document.getElementById("ajoutform")) {
            forme = "ajoutform";
            methode = 'POST';
            alertText = ' Capteur Ajouter ✅'
        }
        else if(document.getElementById("addIntervention")) {
            forme = "addIntervention";
            methode = 'addIntervention';
            alertText = ' Intervention Ajouter ✅'
        }
        else if(document.getElementById("getInterventions")) {
            forme = "getInterventions";
            methode = 'getInterventions';
            terminison = \`/api/probes/maintenace\`
        }
        else {
            forme = "aeroport";
            methode = 'GET';
            terminison = \`/api/airport\`
        }

        const SPAN = document.getElementById("capteurexist");
        if(SPAN){
        SPAN.style.display = 'none';
        }
        const form = document.getElementById(forme);
        
        if (!form) {
            console.error('Form not found');
            return;
        }

        const formData = new FormData(form);
        let check = false;

        formData.forEach((value, key) => {
            //console.log(key, value);
            
            if (methode === "POST" && key === 'serial' && forme == "ajoutform") {
                capteurs.forEach(item => {
                    if (item.serial === value) {
                        SPAN.innerHTML = \`Le capteur "\${value}" existe déjà !\`;
                        SPAN.style.display = 'block';
                        check = true;
                    }
                });
            }
            else if(methode === "GEThistory"){
                    if(key === "serial")
                    entry = value
            }
            else if(methode === "GET"){
                if(forme === "aeroport"){
                    if(key === "airport")
                    entry = value
                }
            }
            else if (methode === "getInterventions"){
                if(key === "serial"){
                    if(value != ''){entry = 'serial='+value; doneSerial=true}
                }
                if(key === "emplacement" && !doneSerial){
                    if(value != ''){entry = 'emplacement='+value; doneEmplacement=true}
                }
                if(key === "DATE" && !doneSerial && !doneEmplacement){
                    if(value != ''){entry = 'DATE='+value;doneDate=true}
                }
                }
            
            else if(methode === "DELETE"){
                    if(key === "serial")
                    {entry = value}
            }

            const elements = document.getElementsByName(key);
            elements.forEach(element => {
                if (element.type === 'file') {
                    if (!element.files || element.files.length === 0) {
                        element.style.boxShadow = "0px 4px 10px rgba(255, 0, 0, 0.5)";
                        check = false;//check = true;
                    } else {
                        element.style.boxShadow = "0px 4px 10px rgba(50, 205, 50, 0.4)";
                    }
                } else {
                    if (value.trim() === '') {
                        element.style.boxShadow = "0px 4px 10px rgba(255, 0, 0, 0.5)";
                        if(!(methode === 'addIntervention'&& key === 'serial'))
                        check = true;
                    } else {
                        element.style.boxShadow = "0px 4px 10px rgba(50, 205, 50, 0.4)";
                    }
                }
            });

        });
        //if (methode === "getInterventions" && !doneSerial && !doneEmplacement && !doneDate)return
        // if (check) return;
        if (tolabo){formData.set('tolabo','tolabo');}
        if(lasttypechange){formData.set('lasttype',lasttype);}

        try {
            let response;
            if(methode === "getInterventions"){
                response = await fetch(\`/api/probes/maintenace?\${entry}\`, {
                method: "GET",
            });
                if (!response.ok) {
                    alertText = \`❌ Error: \${response.statusText}\`;	
                    throw new Error(\`Error: \${response.statusText}\`);
                }

                const data = await response.json();
                if(alertText)alert(alertText)
                else{ 
                //console.log(data)
                entry = entry.split('=')[1]
                populatetableIntervention(data,entry)
                document.getElementById("exampleModal").classList.remove("show");
                document.getElementById("exampleModal").setAttribute("aria-hidden", "true");
                document.body.classList.remove("modal-open");
                document.querySelector(".modal-backdrop").remove();
                document.getElementById("exampleModal").style.display = 'none'; // Hide the modal
                }
                return
            }
            else if(methode === "addIntervention"){
                response = await fetch(\`/api/probes/maintenace\`, {
                method: "POST",
                body: formData,
            });
            }
            else if(methode === "GEThistory"){
                response = await fetch(\`/api/probes/history?serial=\${entry}\`, {
                method: "GET",
            });
            entry = 'hystory'
        }
            else if(methode === "GET"){
                response = await fetch(\`/api/airport?airport=\${encodeURIComponent(entry)}\`, {
                method: methode,
            });
            }
            else if(methode === "DELETE"){
                response = await fetch(\`/api/probes?serial=\${entry}\`, {
                method: methode,
            });
            //console.log("entry : ",entry)
            }
            else{
                    response = await fetch(terminison, {
                    method: methode,
                    body: formData,
                });
            }
            if (!response.ok) {
                alertText = \`❌ Error: \${response.statusText}\`;	
                throw new Error(\`Error: \${response.statusText}\`);
            
            }

            const data = await response.json();
            if(methode === "DELETE"){
                alert(alertText)
                populatetable(data)
                document.getElementById("exampleModal").classList.remove("show");
                document.getElementById("exampleModal").setAttribute("aria-hidden", "true");
                document.body.classList.remove("modal-open");
                document.querySelector(".modal-backdrop").remove();
                document.getElementById("exampleModal").style.display = 'none'; // Hide the modal
                return
            }
            if(alertText)alert(alertText)
        // else{ 
            if(methode === "GEThistory" || forme === "aeroport"){
            populatetable(data,entry)
                document.getElementById("exampleModal").classList.remove("show");
                document.getElementById("exampleModal").setAttribute("aria-hidden", "true");
                document.body.classList.remove("modal-open");
                document.querySelector(".modal-backdrop").remove();
                document.getElementById("exampleModal").style.display = 'none'; // Hide the modal
                return
            }
            
            location.reload()
        } 
        catch (error) {
            alert(' erreur  ❌')
            console.error('Error submitting form:', error);
        }
    }

    function checkmodifydate(X){  
        if(X==1){lastmodify = '1990-01-01'}
        const Value = document.getElementById("input2").value 
        //console.log({lastmodify,Value})
        if(lastmodify != Value){
                const label = document.createElement('label');
                label.style.marginBottom = '0.1rem';
                label.style.marginTop = '0.6rem';
                label.textContent = "Fiche d'étalonnage";
                label.id = 'fichemodif'

                // Create the input element
                const input = document.createElement('input');
                input.className = 'form-control';
                input.name = 'fiche';
                input.type = 'file';
                input.accept = '.pdf';
                input.required = true;

                // Find the element with id="input4"
                const referenceElement = document.getElementById('input4');

                // Insert the label and input after the reference element
                if (referenceElement) {
                    document.getElementById("ajouterFiche").remove();
                    referenceElement.insertAdjacentElement('afterend', label);
                    label.insertAdjacentElement('afterend', input);
                } else {
                    console.error('Element with id="input4" not found.');
                }
        }
        else{
            const label = document.getElementById('fichemodif');
            const input = document.querySelector('input.form-control[name="fiche"]');

            if (label) label.remove();
            if (input) input.remove();

        }
    }

    function createTableHeader() {
        const thead = document.getElementById("search_result_head");
        
        const tr = document.createElement("tr");

        const columns = [
            "Type Capteur",
            "N° Capteur",
            "Emplacement",
        ];

        columns.forEach(text => {
            const th = document.createElement("th");
            th.classList.add("ww");
            th.textContent = text;
            tr.appendChild(th);
        });

        // Create Étalonage Table
        const etalonageTh = document.createElement("th");
        etalonageTh.classList.add("ww");

        const etalonageTable = document.createElement("table");
        etalonageTable.classList.add("w-100");

        const etalonageHeaderRow = document.createElement("tr");
        const etalonageHeader = document.createElement("th");
        etalonageHeader.colSpan = 3;
        etalonageHeader.classList.add("text-center");
        etalonageHeader.textContent = "Étalonage";
        etalonageHeaderRow.appendChild(etalonageHeader);

        const etalonageRow = document.createElement("tr");
        etalonageRow.classList.add("equaltrr");
        ["Date Début", "Date Fin", "Étaloné Par"].forEach(text => {
            const th = document.createElement("th");
            th.textContent = text;
            etalonageRow.appendChild(th);
        });

        etalonageTable.appendChild(etalonageHeaderRow);
        etalonageTable.appendChild(etalonageRow);
        etalonageTh.appendChild(etalonageTable);
        tr.appendChild(etalonageTh);

        // Create Installation Table
        const installationTh = document.createElement("th");
        installationTh.classList.add("ww");

        const installationTable = document.createElement("table");
        installationTable.classList.add("w-100");
        const installationHeaderRow = document.createElement("tr");
        const installationHeader = document.createElement("th");
        installationHeader.colSpan = 2;
        installationHeader.classList.add("text-center");
        installationHeader.textContent = "Installation";
        installationHeaderRow.appendChild(installationHeader);
        const installationRow = document.createElement("tr");
        installationRow.classList.add("equaltr");
        ["Date", "Technicien"].forEach(text => {
            const th = document.createElement("th");
            th.textContent = text;
            installationRow.appendChild(th);
        });

        installationTable.appendChild(installationHeaderRow);
        installationTable.appendChild(installationRow);
        installationTh.appendChild(installationTable);
        tr.appendChild(installationTh);

        thead.appendChild(tr);

        return thead;
    }

    async function getAllAeroprt(){
        
        try {
        AirportData.length=0; // Initialize the array

        for (let emplacement of emplacements) {
            //console.log("Processing:", emplacement);

            const result = await getAirp(emplacement); // Await the async function
            AirportData.push({ [emplacement]: result }); // Store in the desired format
        }

        } catch (error) {
            console.log("Error:", error);
        }

    }
    async function getAirp(airport,index){
        const response = await fetch(\`/api/airport?airport=\${encodeURIComponent(airport)}\`, {
            method: 'GET',
        });
        if (!response.ok) {
            throw new Error(\`Error: \${response.statusText}\`);
        }
        const data = await response.json();
        //console.log(data)
        return data
    }

    async function getAndClassifyProjects() {
        try {
            const response = await fetch(host+'/api/Allprojects'); // replace with your actual endpoint
            const data = await response.json();
            //console.log(data)

            Allprojects = {
            allprojects: data,
            confirmed: [],
            nonconfirmed: []
            };

            data.forEach(project => {
            if (project.confirmed == 0) {
                Allprojects.nonconfirmed.push(project);
                    } else {
                Allprojects.confirmed.push(project);
            }
            });

        } catch (error) {
            console.error('Error fetching or processing data:', error);
        }
    }


   </script>
   </html>
`;