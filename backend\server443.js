const Ticket_Html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Ticketing System</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <!-- jsPDF for PDF generation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>

<!-- html2canvas for chart images -->
<script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
  <style>
  :root {
    /* 2024 Professional Color Palette */
    --primary-color: #1e40af;
    --primary-dark: #1e3a8a;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;
    --light-bg: #f8fafc;
    --dark-bg: #0f172a;
    --card-bg: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --border-color: #e2e8f0;

    /* Modern Gradients 2024 */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-hero: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #581c87 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --gradient-info: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

    /* Glass Morphism 2024 */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-bg-strong: rgba(255, 255, 255, 0.2);
    --glass-border: rgba(255, 255, 255, 0.15);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* Modern Shadows */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
    --shadow-colored: 0 10px 25px -5px rgba(59, 130, 246, 0.15);

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-2xl: 24px;
    --radius-full: 9999px;

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;

    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
  }
  
    body {
    /* Modern 2024 Background */
    background: var(--gradient-hero);
    background-attachment: fixed;
    min-height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--text-primary);
    line-height: 1.6;
    position: relative;
    overflow-x: hidden;
    }

    /* Modern Animated Background */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
        z-index: -1;
        animation: backgroundShift 20s ease-in-out infinite;
    }

    /* Floating Geometric Shapes */
    body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.1) 0%, transparent 50%);
        z-index: -1;
        animation: floatShapes 30s ease-in-out infinite;
    }

    @keyframes backgroundShift {
        0%, 100% {
            opacity: 1;
            transform: scale(1);
        }
        50% {
            opacity: 0.8;
            transform: scale(1.05);
        }
    }

    @keyframes floatShapes {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
        }
        33% {
            transform: translateY(-10px) rotate(120deg);
        }
        66% {
            transform: translateY(5px) rotate(240deg);
        }
    }
        /*
            background-color: #353131;
            color: #6a7077;
            background-repeat: no-repeat;
            background-size: cover;
         */
            z-index: 1;
    }
    /* Floating Elements */
    .floating-element {
        position: absolute;
        opacity: 0.2;
        animation: float 6s ease-in-out infinite;
        pointer-events: none;
        z-index: 0;
        font-size: 3rem;
        color: white;
    }

    .floating-element:nth-child(1) {
        top: 10%;
        left: 10%;
        animation-delay: 0s;
    }

    .floating-element:nth-child(2) {
        top: 20%;
        right: 10%;
        animation-delay: 2s;
    }

    .floating-element:nth-child(3) {
        bottom: 20%;
        left: 15%;
        animation-delay: 4s;
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
        }
        50% {
            transform: translateY(-20px) rotate(180deg);
        }
    }
    .ticket-card {
      margin-bottom: 2rem;
    }
    hr{
      border: 1px solid #ffffff;
      margin-bottom: 3.5rem;
    }
    /* Modern Comment Styles 2024 */
.comment-box {
    background: var(--glass-bg-strong);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    color: var(--text-primary);
    box-shadow: var(--shadow-md);
    transition: var(--transition-base);
}

.comment-box:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: rgba(255, 255, 255, 0.3);
}

.comment-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

/* Modern Ticket List */
#ticketList {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: var(--space-xl);
    color: var(--light-bg);
}

/* Modern Glass Morphism Cards */
.glass-card, .cardi {
    width: 48%;
    border-radius: var(--radius-xl);
    background: var(--glass-bg-strong);
    backdrop-filter: blur(25px) saturate(180%);
    -webkit-backdrop-filter: blur(25px) saturate(180%);
    border: 1px solid var(--glass-border);
    padding: var(--space-xl);
    transition: var(--transition-base);
    z-index: 1;
    height: fit-content;
    box-shadow: var(--shadow-lg), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.glass-card::before, .cardi::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.glass-card:hover, .cardi:hover {
    transform: translateY(-8px) scale(1.02);
    z-index: 9999;
    box-shadow: var(--shadow-2xl), var(--shadow-colored);
    border-color: rgba(255, 255, 255, 0.3);
    background: var(--glass-bg-strong);
}

.glass-card:hover::before, .cardi:hover::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
}



.cardi:hover .btn-xl,
.cardi:hover .btn-xxl {
  display: inline-block;
}

/* Modern Card Title */
.card-title {
    text-align: center;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.9);
    background: var(--glass-bg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    padding: var(--space-md);
    margin-bottom: var(--space-lg) !important;
    font-weight: 600;
    font-size: var(--font-size-lg);
    transition: var(--transition-base);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.card-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.card-title:hover::before {
    left: 100%;
}

.card-title:hover {
    background: rgba(255, 255, 255, 0.95);
    color: var(--primary-color);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}
#submit{
    background-color: #0d6efd;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    width: 100%;
    margin-top: 1.5rem;
}

/* Modern Comment Bubbles 2024 */
.comment {
    width: 50%;
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-xl);
    word-wrap: break-word;
    transition: var(--transition-base);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.comment::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.comment:hover {
    width: 75%;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.comment-left {
    text-align: left;
    background: linear-gradient(135deg, rgba(107, 166, 221, 0.8) 0%, rgba(107, 166, 221, 0.6) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg) var(--radius-lg) var(--radius-lg) var(--radius-sm);
    padding: var(--space-md);
    margin-bottom: var(--space-sm);
    color: white;
}

.comment-right {
    text-align: right;
    background: linear-gradient(135deg, rgba(165, 219, 195, 0.8) 0%, rgba(165, 219, 195, 0.6) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg) var(--radius-lg) var(--radius-sm) var(--radius-lg);
    padding: var(--space-md);
    margin-bottom: var(--space-sm);
    margin-left: auto;
    color: var(--text-primary);
}


.comment-name {
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 4px;
}
.comment-text{
  font-size: 0.9rem;
  line-height: 1.4;
  color: #5e5c5c;
}
.Pbadge{
    font-size: 1.2rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); 
    position: inherit;
    color:white;
    
}
.blinking {
    color: white !important;
    animation: blink 1s ease-in-out infinite;
}
@keyframes blink {
    0%, 100% {
        opacity: 1;
        box-shadow: 0px 0px 0px 2px white;
    }
    50% {
        opacity: 0.5;
        box-shadow: 0px 0px 1px 1px #010813;
    }
}
/* Modern Button Styles 2024 */
.btn {
    position: relative;
    overflow: hidden;
    font-weight: 600;
    border: none;
    transition: var(--transition-base);
    border-radius: var(--radius-md);
    padding: 0.75rem 1.5rem;
    box-shadow: var(--shadow-md);
    text-transform: uppercase;
    letter-spacing: 0.05rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
}

.btn-success {
    background: var(--gradient-success);
    color: white;
}

.btn-warning {
    background: var(--gradient-warning);
    color: white;
}

.btn-danger {
    background: var(--gradient-danger);
    color: white;
}

.btn-info {
    background: var(--gradient-info);
    color: white;
}

.btn-xxl{
    width: 15%;
    display: none;
    transition: all 0.4s ease;
    font-size: 1.2rem;
    padding: 0.75rem 1rem;
}

.btn-xxl i{
    font-size: 1.9rem;
}

.btn-xl{
    font-size: 1.2rem;
    padding: 0.75rem 1.25rem;
    border-radius: var(--radius-md);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    box-shadow: var(--shadow-md);
    display: none;
    transition: all 0.3s ease;
}
.hidden {
    display: none;
}
.blue {
  border-left: 4px solid #4895ff;
}

.green {
  border-left: 8px solid #3bb54a;
}

.red {
  border-left: 8px solid #b3404a;
}
.yellow {
    border-left: 8px solid #f9c74f;
}
.purple {
    border-left: 8px solid #9d4edd;
}
.orange {
    border-left: 8px solid #f3722c;
}
.h5- p{
    position: absolute;
    text-align: center;
    
}
nav{
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 99.6vh;
    background-color: #18191ac7;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);    
    position: absolute;
    color: snow;
    top: 0.3vh;
    left: 0;
    width: 206px;
    padding: 10px 7px;
    z-index: 1000;
    border-right: 1px solid #747272;
    border-radius: 6px;
    transition: width 2s ease-in-out, display 0.8s ease-in-out;
    overflow: hidden;
}
nav button {
    width: 98%;
    margin-bottom: 30px;
    font-size: 1.2rem;
    padding: 10px;
    color: white;
    transition: background-color 0.3s, transform 0.3s;
    align-content: left;
}
nav button:hover {
    border: 1px solid #ffffff !important;
    transform: scale(1.05);
}
nav button i {
    font-size: 1.3rem;
    margin-right: 0.5rem;
}
h1 {
    font-size: 3.8rem;
    color: #0c0c0c;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 2.5rem;
    margin-top: 2.5rem;
}
.notranslate {
    unicode-bidi: isolate;
  }

  .time-display {
    display: flex;
    align-items: center;
    gap: 10px;
}
.time-display i {
    font-size: 1.5rem;
    color: #4BC0C0;
}
.time-display .value {
    font-size: 1.2rem;
    font-weight: bold;
}
.time-display .label {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
}

.status-open { color: #FF6384; font-weight: bold; }
.status-closed { color: #36A2EB; font-weight: bold; }

.table-responsive {
    max-height: 500px;
    overflow-y: auto;
}
.BULGE{
    animation: bulge .5s forwards;  
}

@media print {
    body * {
        visibility: hidden;
    }
    .modal-content {
        visibility: visible;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
}
@keyframes bulge {
    50% {
      transform: rotate(4deg);
      font-weight: bold;
    }
    100% {
      transform: rotate(0);
      font-weight: bold;
    }
}
/* Modern Form Styles 2024 */
.form-control, .form-select {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    padding: var(--space-md) var(--space-lg);
    transition: var(--transition-base);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
}

.form-control:focus, .form-select:focus {
    background: rgba(255, 255, 255, 0.95);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25), var(--shadow-md);
    outline: none;
    transform: translateY(-2px);
}

.form-label {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--space-sm);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.05rem;
}

.form-check {
    margin-bottom: var(--space-md);
}

.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-sm);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: var(--transition-base);
}

.form-check-input:checked {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.form-check-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-left: var(--space-sm);
}

input[type="radio"] {
    transform: scale(1.7);
    margin-bottom: 10px;
}

/* Modal Enhancements */
.modal-content {
    background: var(--glass-bg-strong);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-2xl);
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-body {
    background: rgba(255, 255, 255, 0.05);
}

.modal-footer {
    background: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

/* Modern Animations & Micro-interactions 2024 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

.shimmer-effect {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    background-size: 200px 100%;
    animation: shimmer 2s infinite;
}

/* Loading States */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-success);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    background: var(--glass-bg-strong);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    animation: slideInRight 0.5s ease-out;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .glass-card, .cardi {
        width: 100%;
        margin-bottom: var(--space-lg);
    }

    .comment {
        width: 80%;
    }

    .comment:hover {
        width: 95%;
    }

    .btn {
        padding: var(--space-sm) var(--space-md);
        font-size: var(--font-size-sm);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --glass-bg: rgba(0, 0, 0, 0.2);
        --glass-bg-strong: rgba(0, 0, 0, 0.3);
        --glass-border: rgba(255, 255, 255, 0.1);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .glass-card, .cardi {
        border-width: 2px;
        border-color: rgba(255, 255, 255, 0.5);
    }

    .btn {
        border: 2px solid rgba(255, 255, 255, 0.3);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
  </style>
</head>
<body>
<!-- Floating Background Elements -->
<div class="floating-element">⚙️</div>
<div class="floating-element">🌡️</div>
<div class="floating-element">💧</div>
<div class="floating-element">🌪️</div>
<div class="floating-element">📊</div>
  <button class="navbar-toggler" type="button"onClick="OpenNavbar()">
    <i class="bi bi-list" style="left: 20px;font-size:50px;position:fixed;color:white;"></i>
  </button>
    <nav id="navbar" class="notranslate" translate="no">
        <button class="btn btn-success" onclick="window.location.href='/'">
        <i class="bi bi-house"></i> Acceuil
      </button>
          <button class="btn btn-secondary" onclick="MyTickets()">
        <i class="bi bi-person"></i> Mes Tickets
    </button>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#TicketModal">
      <i class="bi bi-plus-circle"></i> Nouveau Ticket
    </button>
    <button class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#MonsuelleModal">
    <i class="bi bi-clipboard-data"></i> Statistique
   </button>
    <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#CSVModal">
    <i class="bi bi-filetype-csv"></i> CSV Tickets
  </button>

        <div style="position:absolute;bottom: 0.5rem;left: 0.2rem;width: 98%;">
        <strong>Nom: </strong><span id="userName"></span><br>
       <strong>IP: </strong><span id="ipAddress"></span>
        <button class="btn btn-danger" onclick="window.location.href='/logout'">
            <i class="bi bi-box-arrow-right"></i> Déconnexion
          </button>
        </div>
    </nav>
  <div class="container py-6">
    <h1 class="text-center text-white" style="margin-bottom:6rem !important;">Gestion de Tickets</h1>
    
    <div class="d-flex justify-content-around flex-nowrap align-items-center mb-3">
    <div class="d-flex flex-wrap gap-3">
        <!-- Total Tickets -->
        <div class="card border-white" style="width: 15.5rem;">
            <div class="card-body text-center p-3"style="color: white;background-image: radial-gradient( circle 404px at 20.3% 15.9%,  rgba(0,79,255,1) 0%, rgba(0,240,255,1) 90% );">
                <div class="d-flex justify-content-center align-items-center gap-2 mb-2">
                    <i class="bi bi-ticket-detailed text-white fs-4"></i>
                    <span class="h5 mb-0 text-white">Total</span>
                </div>
                <span id="totalTickets" class="h2 fw-bold text-white">0</span>
            </div>
        </div>

        <!-- Open Tickets -->
        <div id="openedTicketsbody" class="card border-white" style="width: 15.5rem;">
            <div class="card-body text-center p-3"style="color:black;background-image: radial-gradient( circle farthest-corner at 10% 20%,  rgba(246,248,79,1) 0%, rgba(249,192,101,1) 90% );">
                <div class="d-flex justify-content-center align-items-center gap-2 mb-2">
                    <i class="bi bi-folder2-open text-black fs-4"></i>
                    <span class="h5 mb-0 text-black">Ouverts</span>
                </div>
                <span id="openedTickets" class="h2 fw-bold text-black">0</span>
            </div>
        </div>

        <!-- Closed Tickets -->
        <div class="card border-white" style="width: 15.5rem;">
            <div class="card-body text-center p-3" style="color:white;background-image: radial-gradient( circle farthest-corner at 10% 20%,  rgba(57,181,51,1) 0%, rgba(35,91,32,1) 90% );">
                <div class="d-flex justify-content-center align-items-center gap-2 mb-2">
                    <i class="bi bi-check-circle text-white fs-4"></i>
                    <span class="h5 mb-0 text-white">Fermés</span>
                </div>
                <span id="closedTickets" class="h2 fw-bold text-white">0</span>
            </div>
        </div>

        <!-- Average Time -->
        <div id="averageTimebody" class="card border-white" style="width: 15.5rem;">
            <div class="card-body text-center p-3" style="background-image: radial-gradient( circle farthest-corner at 10% 20%,  rgba(202,248,255,1) 0%, rgba(186,204,227,1) 51.2%, rgba(117,144,179,1) 100.1% );">
                <div class="d-flex justify-content-center align-items-center gap-2 mb-2">
                    <i class="bi bi-clock-history text-black fs-4"></i>
                    <span class="h5 mb-0 text-black">Moyen</span>
                </div>
                <span id="averageTime" class="h2 fw-bold text-black">0 heure</span>
            </div>
        </div>
    </div>

</div>
    <hr>


    <!-- Ticket Submission Form -->
    <div class="modal fade notranslate" id="TicketModal" tabindex="-1" aria-labelledby="TicketModalLabel" translate="no">
        <div class="modal-dialog modal-l modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="missionModalLabel">Nouveau Ticket</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
    <div class="card mb-5 shadow-sm">
      <div class="card-body">
        <form id="ticketForm">
          <div class="mb-3">
            <label for="system_type" class="form-label">Système</label>
            <input type="text" class="form-control" id="system_type" required />
          </div>
          <div class="mb-3">
            <label for="description" class="form-label">Description ( Max 50 charactères )</label>
            <input class="form-control" id="description" maxlength="50" required />
          </div>
          <div class="mb-3">
          <label for="emplacement" class="form-label">emplacement ( Max 50 charactères )</label>
          <input class="form-control" id="emplacement" maxlength="50" required />
        </div>
          <div id="checkboxes" class="mb-3" style="border: 1px solid rgb(206, 202, 202);border-radius: 5px; padding: 10px 10px 10px 10px;">
            <label class="form-label d-block mb-2">Service Concerné</label><br>
            <div style=" text-align:center;">
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="checkbox" name="tunis" id="tunis">
              <label class="form-check-label" for="subtunis">Tunis</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="checkbox" name="sfax" id="sfax">
              <label class="form-check-label" for="subsfax">Sfax</label>
            </div>
            <!--<div class="form-check form-check-inline">
              <input class="form-check-input" type="checkbox" name="sousse" id="sousse">
              <label class="form-check-label" for="sousse">Sousse</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="checkbox" name="jendouba" id="jendouba">
              <label class="form-check-label" for="jendouba">Jendouba</label>
            </div>-->
            <span id="alert" class="text" style="font-size: 0.8rem;color:red; display: block; margin-top: 5px;"></span>
          </div>
        </div>
          <!--<div class="mb-3 hidden">
            <label for="reporter_name" class="form-label">Nom du reporteur</label>
            <input type="text" class="form-control" id="reporter_name" required />
          </div>-->
          <button id="submit" type="submit" class="btn btn-primary">Valider</button>
        </form>
      </div>
    </div>
</div>
</div>
</div>
    </div>

    <div class="modal fade notranslate" id="CSVModal" tabindex="-1" aria-labelledby="CSVModalLabel" translate="no">
    <div class="modal-dialog modal-l modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="CSVModalLabel">Télécharger CSV</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="card mb-5 shadow-sm">
                <div class="card-body">
                    <form id="CSVForm">
                        <div class="mb-3">
                        <label for="startDate" class="form-label">Date Début</label>
                        <input type="date" name="FROM" class="form-control" id="startDate" required />
                        <label for="endDate" class="form-label">Date Fin</label>
                        <input type="date" name="TO" class="form-control" id="endDate" required />
                        </div>
                        <span id="alert1" class="text" style="font-size: 0.8rem;color:red; display: block; margin-top: 5px;"></span>
                        <button id="CSVSubmit" type="submit" class="btn btn-primary" style="position:relative;left:36%;">Télécharger</button>
                    </form>
                </div>
            </div>
            </div>
        </div>
        </div>
    </div>

    <div class="modal fade notranslate" id="MonsuelleModal" tabindex="-1" aria-labelledby="MonsuelleModalLabel" translate="no">
    <div class="modal-dialog modal-l modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Choisie un Mois</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="card mb-5 shadow-sm">
                    <div class="card-body">
                            <form id="MonthForm">
                            <!-- Month input and radio buttons -->
                            <div class="mb-3">
                                <label for="Mois" class="form-label">Mois</label>
                                <input type="month" name="recapMonth" class="form-control" id="Mois" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Représentation:</label>
                                <div class="d-flex flex-row justify-content-around"> 
                                    <div class="form-check">
                                        <input type="radio" id="table" name="representation" value="table" class="form-check-input">
                                        <label for="table" class="form-check-label">Tableau <i class="bi bi-table"></i></label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" id="graph" name="representation" value="graph" class="form-check-input" checked>
                                        <label for="graph" class="form-check-label">Graphique <i class="bi bi-bar-chart-line"></i> </label>
                                    </div>
                                </div>
                            </div>
                            <button id="MonthSubmit" type="submit" style="position: relative;left:35%;" class="btn btn-primary mt-3">Recherche</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <span id="alert2" class="text-danger small"></span>
            </div>
        </div>
    </div>
</div>

    <!-- Ticket List -->
    <div id="ticketList" class="d-flex justify-content-between">
        </div>
  </div>

  <!-- Modal -->
<div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="staticBackdropLabel"></h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">

  
      <!-- Chart Container -->
      <div id="chartContainer" class="mt-4">
          <div class="row">
              <div class="col-md-6">
                  <canvas id="statusChart"></canvas>
              </div>
              <div class="col-md-6">
                  <div class="card">
                      <div class="card-body">
                          <h5 class="card-title">Temps de résolution</h5>
                          <div id="avgTime"></div>
                      </div>
                  </div>
                  <div class="mt-4">
                      <canvas id="systemChart"></canvas>
                  </div>
              </div>
          </div>
      </div>
  
      <!-- Table Container -->
      <div id="tableContainer" class="mt-4" style="display: none;">
          <div class="table-responsive">
              <table id="ticketTable" class="table table-striped">
                  <thead>
                      <tr>
                          <th>ID</th>
                          <th>Système</th>
                          <th>Subdivision</th>
                          <th>Emplacement</th>
                          <th>Statut</th>
                          <th>Créé le</th>
                          <th>Temps de résolution</th>
                      </tr>
                  </thead>
                  <tbody></tbody>
              </table>
          </div>
      </div>
  </div>
      <div class="modal-footer">
        <button type="button" id="printRepresentation" class="btn btn-secondary"><i class="bi bi-printer"></i> Imprimer</button>
      </div>
    </div>
  </div>
</div>

  <script>
  document.addEventListener("keydown", e => {
    if (e.ctrlKey && e.key === "u") {
      alert("❌ C'est interdit de faire ça !");
      e.preventDefault();
    }
  });
  
  function OpenNavbar() {
    const navbar = document.getElementById('navbar');
    if (navbar.style.display === 'none' || navbar.style.display === '') {
      navbar.style.display = 'flex';
      navbar.style.width = '206px';
    } else {
      navbar.style.display = 'none';
      navbar.style.width = '0';
    }
  }
  //if clicked outside the navbar, close it
  document.addEventListener('click', function(event) {
    const navbar = document.getElementById('navbar');
    const toggler = document.querySelector('.navbar-toggler');
    if (!navbar.contains(event.target) && !toggler.contains(event.target)) {
      navbar.style.display = 'none';
      navbar.style.width = '0';
    }
  });
  
    const host = '';//"http://localhost:80";
    let TicketIds =[];
    let AllTickets = '';
    let Summarydata = {};
    const MyName = sessionStorage.getItem('Me') || 'Anonyme';
    document.getElementById('CSVForm').addEventListener('submit', async function (e) {
        e.preventDefault();
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
      
        const FROM = document.getElementById('startDate').value;
        const TO = document.getElementById('endDate').value;
        const alertEl = document.getElementById('alert1');
      
        // Reset alerts
        alertEl.textContent = "";
      
        // Validate dates
        if (!FROM || !TO) {
          alertEl.textContent = "* Veuillez remplir les deux dates.";
          submitBtn.disabled = false;
          return;
        }
      
        const fromDate = new Date(FROM + 'T00:00:00');
        const toDate = new Date(TO + 'T23:59:59');
      
        if (fromDate > toDate) {
          alertEl.textContent = "* date de début doit être antérieure à la date de fin.";
          submitBtn.disabled = false;
          return;
        }
      const queryString = new URLSearchParams({ FROM, TO }).toString();
        try {
          const response = await fetch(host + '/api/export/tickets.csv?'+queryString, {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' }
            //body: JSON.stringify({ FROM, TO })
          });
      
          if (!response.ok) {
            const error = await response.text();
            throw new Error(error || 'Erreur serveur');
          }
      
          const contentType = response.headers.get('Content-Type');
          if (!contentType || !contentType.includes('csv')) {
            throw new Error('* Réponse serveur invalide');
          }
      
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = \`tickets_\${FROM}_\${TO}.csv\`; // Dynamic filename
          document.body.appendChild(a);
          a.click();
          a.remove();
          window.URL.revokeObjectURL(url);
          // Close the modal
          const modal = bootstrap.Modal.getInstance(document.getElementById('CSVModal'));
          modal.hide();
      
        } catch (error) {
          console.error('Erreur:', error);
          alertEl.textContent = \`Erreur: \${error.message}\`;
          submitBtn.disabled = false;
        } 
      });

    document.getElementById('ticketForm').addEventListener('submit', async function (e) {
      e.preventDefault();
      const checkboxesnames = validateCheckboxes(e); // Validate checkboxes and get names
    if (checkboxesnames == false) { 
        document.getElementById('checkboxes').style.border = '2px solid red'; // Highlight checkboxes if validation fails
        return;
    }

      const ticket = {
        system_type: document.getElementById('system_type').value,
        description: document.getElementById('description').value,
        emplacement: document.getElementById('emplacement').value,
        Subdivision: checkboxesnames.join(' | ')
        //reported_by: document.getElementById('reporter_name').value
      };
      await fetch('/api/tickets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(ticket)
      });
      this.reset();
      loadTickets();
    });

    const user = sessionStorage.getItem('Me') || 'Anonyme';
    document.getElementById('userName').textContent = user;
    const Sessiondata = JSON.parse(sessionStorage.getItem("sessiondata")) || {};
    document.getElementById('ipAddress').textContent = Sessiondata.ip|| 'Inconnu';

    function validateCheckboxes(event) {
    const checkboxes = document.querySelectorAll('.form-check-input');
    let oneChecked = false;
        let checkedName = [];
    checkboxes.forEach((checkbox) => {
      if (checkbox.checked) {
        checkedName.push(checkbox.id);
        oneChecked = true;
      }
    });
    if (checkedName.length > 1) {
        document.getElementById('checkboxes').style.border = '2px solid red'; // Highlight checkboxes if no service is selected
        document.getElementById('alert').textContent = "* Veuillez cocher un seul service concerné.";
        event.preventDefault(); 
        return false;
      }
      if (!oneChecked) {
        event.preventDefault(); // stop form submission
        document.getElementById('alert').textContent = "* Veuillez cocher un service concerné.";
        return false;
      }
      document.getElementById('alert').textContent = "";
    return checkedName;
  }

  
  // Load Chart.js if not already loaded (add to HTML head)
 
  // DOM Elements
const monthForm = document.getElementById('MonthForm');
const chartContainer = document.getElementById('chartContainer');
const tableContainer = document.getElementById('tableContainer');
const statusChartCanvas = document.getElementById('statusChart');
const systemChartCanvas = document.getElementById('systemChart');
const avgTimeElement = document.getElementById('avgTime');
const alertElement = document.getElementById('alert2');

// Chart instances
let statusChart = null;
let systemChart = null;

// Main form handler
monthForm.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // Reset UI
    alertElement.textContent = '';
    showLoading(true);
    
    try {
        const formData = new FormData(monthForm);
        const month = formData.get('recapMonth');
        const representation = formData.get('representation');
        
        // Fetch data from server
        const { stats, tickets } = await fetchTicketData(month);
        
        // Format month for display (French locale)
        const monthDisplay = new Date(month + '-01').toLocaleDateString('fr-FR', {
            month: 'long',
            year: 'numeric'
        }).replace(' ', ' ');
        
        // Update UI based on representation choice
        if (representation === 'graph') {
            document.getElementById("staticBackdropLabel").textContent = \`Graphique - \${monthDisplay}\`;
            renderCharts(stats);
            showVisualization('chart');
        } else {
            document.getElementById("staticBackdropLabel").textContent = \`Tableau - \${monthDisplay}\`;
            renderTable(tickets);
            showVisualization('table');
        }
        
    } catch (error) {
        console.error("Error:", error);
        alertElement.textContent = 'Erreur lors de la récupération des données';
    } finally {
        const currentModal = bootstrap.Modal.getInstance(document.getElementById('MonsuelleModal'));
        const nextModal = new bootstrap.Modal(document.getElementById('staticBackdrop'));
        showLoading(false);
        wait(2000);
        // Hide current modal
        currentModal.hide();
        
        // When current modal is fully hidden, show next one
        document.getElementById('MonsuelleModal').addEventListener('hidden.bs.modal', function() {
            nextModal.show();
            
            // Remove the event listener to prevent memory leaks
            this.removeEventListener('hidden.bs.modal', arguments.callee);
        });
        
    }
});

// Fetch data from server
async function fetchTicketData(month) {
    const response = await fetch(\`/api/monsuelle?month=\${month}\`);
    
    if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new Error(error.message || 'Erreur serveur');
    }
    
    return await response.json();
}

// Render charts
function renderCharts(stats) {
    // IMPORTANT: Set white background for PDF export
    Chart.defaults.backgroundColor = '#FFFFFF';
    Chart.defaults.borderColor = '#DDDDDD';
    // Destroy existing charts
    if (statusChart) statusChart.destroy();
    if (systemChart) systemChart.destroy();
    
    // 1. Ticket Status Pie Chart
    statusChart = new Chart(statusChartCanvas, {
        type: 'pie',
        data: {
            labels: ['Ouverts', 'Fermés'],
            datasets: [{
                data: [stats.statusCount.open, stats.statusCount.closed],
                backgroundColor: ['#FF6384', '#36A2EB'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: { position: 'bottom' },
                tooltip: {
                    callbacks: {
                        label: (context) => {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((context.raw / total) * 100);
                            return \`\${context.label}: \${context.raw} (\${percentage}%)\`;
                        }
                    }
                }
            }
        }
    });
    
    // 2. System Type Bar Chart
    systemChart = new Chart(systemChartCanvas, {
        type: 'bar',
        data: {
            labels: Object.keys(stats.systemTypes),
            datasets: [{
                label: 'Tickets',
                data: Object.values(stats.systemTypes),
                backgroundColor: '#4BC0C0',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: { stepSize: 1 }
                }
            },
            plugins: {
                legend: { display: false }
            }
        }
    });
    
    // 3. Display average resolution time
    renderAverageTime(stats.avgResolutionHours);
}

// Render average time
function renderAverageTime(hours) {
    let timeText;
    
    if (hours > 0) {
        if (hours < 24) {
            timeText = \`\${hours.toFixed(1)} heures\`;
        } else {
            const days = Math.floor(hours / 24);
            const remainingHours = (hours % 24).toFixed(1);
            timeText = \`\${days}j \${remainingHours}h\`;
        }
    } else {
        timeText = "Aucun ticket résolu et fermé";
    }
    
    avgTimeElement.innerHTML = \`
        <div class="time-display">
            <i class="bi bi-clock"></i>
            <div>
                <span class="value">\${timeText}</span>
                <span class="label">Temps moyen de résolution</span>
            </div>
        </div>
    \`;
}

// Render table view
function renderTable(tickets) {
    const tableBody = document.querySelector('#ticketTable tbody');
    tableBody.innerHTML = '';
    
    tickets.forEach(ticket => {
        const row = document.createElement('tr');
        
        // Calculate resolution time if closed
        let resolutionTime = '';
        if (ticket.status === 'closed' && ticket.updated_at) {
            const hours = (new Date(ticket.updated_at) - new Date(ticket.created_at)) / (1000 * 60 * 60);
            resolutionTime = hours < 24 
                ? \`\${hours.toFixed(1)}h\` 
                : \`\${Math.floor(hours/24)}j \${(hours%24).toFixed(1)}h\`;
        }
        
        row.innerHTML = \`
            <td>\${ticket.id}</td>
            <td>\${ticket.system_type}</td>
            <td>\${ticket.subdivision}</td>
            <td>\${ticket.emplacement}</td>
            <td class="status-\${ticket.status}">\${ticket.status === 'open' ? 'Ouvert' : 'Fermé'}</td>
            <td>\${new Date(ticket.created_at).toLocaleDateString()}</td>
            <td>\${resolutionTime}</td>
        \`;
        
        tableBody.appendChild(row);
    });
}

// UI Helpers
function showVisualization(type) {
    chartContainer.style.display = type === 'chart' ? 'block' : 'none';
    tableContainer.style.display = type === 'table' ? 'block' : 'none';
}

function showLoading(show) {
    const submitBtn = document.getElementById('MonthSubmit');
    submitBtn.disabled = show;
    submitBtn.innerHTML = show 
        ? '<span class="spinner-border spinner-border-sm" role="status"></span> Chargement...'
        : 'Recherche';
}

// Initialize default month
window.addEventListener('DOMContentLoaded', () => {
    const now = new Date();
    const currentMonth = \`\${now.getFullYear()}-\${String(now.getMonth() + 1).padStart(2, '0')}\`;
    document.getElementById('Mois').value = currentMonth;
});

    async function loadTickets(Me) {
        let tickets,SumOfTickets=0,SumOfOpened=0,SumOfClosed=0,TraitementAverage=0;
  if(!Me){
        const res = await fetch(\`/api/tickets\`);
        tickets = await res.json();
        AllTickets = tickets;
        //console.log("AllTickets:", tickets);
    }
    else{tickets = Me}
  const container = document.getElementById('ticketList');
  container.innerHTML = '';

    const classes = [" green", " red", " blue", " yellow", " purple", " orange"];
    let classIndex = 0;
    TicketIds =[];
  for (const ticket of tickets) {
    const card = document.createElement('div');
    card.className = 'cardi mb-3 shadow-sm'+ classes[classIndex % classes.length];
    classIndex++;
    SumOfTickets++;
    ticket.status === 'closed' ? SumOfClosed++ : SumOfOpened++;
    (ticket.status === 'closed' && ticket.resolu === 'oui') ? TraitementAverage += (new Date(ticket.resolu_at) - new Date(ticket.created_at)) : TraitementAverage += 0;
    card.style.position = '';
    const ticketId = \`ticket-\${ticket.id}\`;
    const commentBoxId = \`comment-box-\${ticket.id}\`;

    card.innerHTML = \`
      <div class="card-body">
        <div class="card-title d-flex justify-content-between align-items-center mb-2"  data-bs-toggle="collapse" data-bs-target="#\${commentBoxId}" aria-expanded="false" onClick="showCommentArea('commentarea-\${ticket.id}',false)">
            <h5 class="mb-0">
                #\${ticket.id} - \${ticket.system_type}
        </h5>
            <p class="mb-0 notranslate" translate="no">
                <span class="Pbadge bg-\${ticket.status === 'closed' ? 'success' : 'danger'} \${ticket.status === 'closed' ? '' : 'blinking'}">
                \${ticket.status === 'closed' ? '<i class="bi bi-lock"></i>' : '<i class="bi bi-unlock"></i>'}&nbsp;
                \${ticket.status}
          </span>
        </p>
        </div>

        <div class="mb-3 d-flex flex-row justify-content-between align-items-start">
            <div style="max-width:33%;">
                <p class="card-text"><strong>Anomalie:</strong><br> \${ticket.description}</p>
            </div>
            <div>
                <p class="card-text"><strong>Reporté par:</strong><br> \${ticket.reported_by}</p>
            </div>
            <div>
                <p class="card-text"><strong>Crée le:</strong><br> \${ticket.created_at.split('.')[0].replace('T',' ')}</p>
            </div>
        </div>


        <div id="\${commentBoxId}" class="collapse mt-3">
        <div class="comment-box">
        <h6>Commentaires:</h6>
        <div class="comment-container mb-3 text-break" id="comments-\${ticket.id}">
          <div class="text-muted">Chargement...</div>
        </div>
      
        \${ticket.status !== 'closed' ? \`
          <form id="commentarea-\${ticket.id}" class="mt-2 hidden" onsubmit="submitComment(event, \${ticket.id})">
            <div class="mb-3 d-flex flex-row justify-content-center align-items-center">
              <textarea style="width:80%;margin-right:1%;" name="comment" class="form-control" placeholder="Ajouter un commentaire" rows="2" required></textarea>
              <button type="submit" title="envoyer" class="btn btn-outline-primary btn-xxl"><i class="bi bi-send"></i></button>
            </div>
            <div class="d-flex flex-column justify-content-center align-items-center mb-1"style="border:1px solid grey;border-radius:5px;box-shadow:inset -1px 0px 6px 2px #010813">
                <h6 id="drapeau-\${ticket.id}" class="BULGE" style="font-size: 1.2em !important;margin-top:16px;margin-bottom:16px;">Problème Résolu ?</h6>
                <div class="mb-3 d-flex flex-row justify-content-center justify-content-around align-items-center"style="gap:3rem">
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <input type="radio" name="resolu" id="resolu-\${ticket.id}-oui" class="me-3">
                        <label for="resolu-\${ticket.id}-oui" class="me-2">
                            <i class="bi bi-check-circle"></i> Oui
                        </label>
                    </div>
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <input type="radio" name="resolu" id="resolu-\${ticket.id}-non" checked>
                        <label for="resolu-\${ticket.id}-non" class="me-2">
                            <i class="bi bi-x-circle"></i> Non
                        </label>
                    </div>
                </div>
            </div>
          </form>
      
          <div class="btn-x d-flex justify-content-center align-items-center gap-2">
            <button type="button" onClick="showCommentArea('commentarea-\${ticket.id}', true)" class="btn btn-outline-warning btn-xl"><i class="bi bi-pen"></i> Commenter</button>
            <button type="button" onClick="CloseTicket('\${ticket.id}')" class="btn btn-outline-danger btn-xl"><i class="bi bi-x-octagon"></i> Fermer Ticket</button>
          </div>
        \` : \`
        <div class="d-flex flex-column"style="border:2px solid grey; border-radius:5px;padding-left:20px;">
          <span style="padding:5px;">
            \${ticket.status === 'closed'
              ? \`<i class="bi bi-tools"></i> Résolu Par : \${ticket.resolu_technicien} le \${new Date(ticket.resolu_at).toLocaleString('fr-FR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              })}</span>
              <span style="padding:5px;">
              <i class="bi bi-key"></i> Fermé par : \${ticket.updated_by} le \${new Date(ticket.updated_at).toLocaleString('fr-FR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              })}\`
              : ''}
          </span>
          </div>
        \`}
      </div>
      
        </div>
        <div class="d-flex justify-content-center align-items-center mt-3">
            <p style="color:white; font-size:1.3rem;margin-top:1.5rem;margin-bottom:-0.5rem;box-shadow: 0.5px 0.5px 1px 1px white;;padding: 6px;border-radius: 8px;">\${ticket.emplacement}</p>
        </div>
      </div>
      \`;
      TicketIds.push(ticket.id); // Store ticket ID for later use
  // Load comments only when collapsed section is shown
card.querySelector(\`[data-bs-target="#\${commentBoxId}"]\`).addEventListener('click', async () => {
  const commentBox = document.getElementById(\`comments-\${ticket.id}\`);
  if (!commentBox.dataset.loaded) {
    const commentRes = await fetch(\`\${host}/api/tickets/\${ticket.id}/comments\`);
    const comments = await commentRes.json();

    let lastCommenter = null;
    let lastDirection = 'left';
    let cr_date;

    commentBox.innerHTML = comments.length > 0
      ? comments.map(c => {
          if (c.commenter_name === lastCommenter) {
            // Same commenter, keep direction
          } else {
            // Switch direction for a new commenter
            lastDirection = lastDirection === 'left' ? 'right' : 'left';
            lastCommenter = c.commenter_name;
          }
          cr_date = new Date(c.created_at).toLocaleString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
          return \`
                <div class="comment comment-\${lastDirection}">
                <div class="comment-name">\${c.commenter_name}</div>
                    <div class="comment-text">\${c.comment}</div>
                <br>
                <p style="font-size:0.8rem;margin-top: 0.1rem;margin-bottom: -0.5rem;">\${cr_date}</p>
                </div>
            \`;
        }).join('')
      : '<p class="text-muted">Aucun commentaire.</p>';

    commentBox.dataset.loaded = true;
  }
});
    container.appendChild(card);
  }
  const averageTime = ((TraitementAverage / 1000) / 60) / SumOfClosed;
  const averageTimeFormatted = averageTime ? \`\${(averageTime/60).toFixed(1)} heures\` : 'Aucune donnée';
  Summarydata = {
    totalTickets: SumOfTickets,
    openedTickets: SumOfOpened,
    closedTickets: SumOfClosed,
    averageTime: averageTimeFormatted
  };
    document.getElementById('totalTickets').textContent = Summarydata.totalTickets;
    document.getElementById('openedTickets').textContent = Summarydata.openedTickets;
    if(Number(Summarydata.openedTickets) > 4) {document.getElementById('openedTicketsbody').classList.add("blinking");}
    document.getElementById('closedTickets').textContent = Summarydata.closedTickets;
    document.getElementById('averageTime').textContent = Summarydata.averageTime;
    if(Number(averageTimeFormatted.split('.')[0]) > 24) {
        const timeNumber = document.getElementById('averageTime');
        timeNumber.classList.replace('text-black', 'text-danger');
        document.getElementById('averageTimebody').classList.add("blinking");
    }
    else{document.getElementById('averageTime').style.color = 'black';}

  if (tickets.length === 0) {
    container.innerHTML = '<div class="text-center text-muted">Aucun ticket trouvé.</div>';
  }
}
async function CloseTicket(ticketId) {
    // Step 1: Confirm before closing
    const confirmClose = confirm("🛑 Êtes-vous sûr de vouloir fermer ce ticket ?");
    if (!confirmClose) return;
  
    // Step 2: Check if the ticket belongs to the current user
    const myTicket = AllTickets.find(ticket => 
      ticket.id === ticketId && ticket.reported_by === MyName
    );
  
    if (!myTicket) {
      alert("❌ Vous n'avez pas le droit de fermer ce ticket\\nouvert par une autre personne.");
      return;
    }
  
    let data = { status: 'closed' };
  
    // Step 3: If ticket is marked as not resolved, confirm again
    if (myTicket.resolu === 'non') {
      const confirmNonResolu = confirm("⚠️ Le problème est mentionné comme 'Non Résolu'.\\nÊtes-vous sûr de vouloir fermer ce ticket ?");
      if (!confirmNonResolu) return;
  
      const cause = prompt("💬 Veuillez indiquer la cause de non-résolution :");
      if (cause && cause.trim() !== "") {
        data.cause = cause.trim();
      } else {
        alert("❗ Fermeture annulée : cause de non résolution requise.");
        return;
      }
    }
  
    try {
      const res = await fetch(\`/api/tickets/\${ticketId}\`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
        if (res.ok) {
            alert("✅ Ticket fermé avec succès.");
            loadTickets();
        } else {
            alert("❌ Erreur lors de la fermeture du ticket.");
        }
    }catch(error){
        console.log(error);
        alert("❌ Erreur lors de la fermeture du ticket.");
    }
}

function showCommentArea(id, value) {
    console.log("showCommentArea id:", id);
    const commentArea = document.getElementById(id);
  
    if (value === true) {
      commentArea.classList.remove('hidden');
        const str = "drapeau-" + id.split('-')[1]
      const flagElement = document.getElementById(str);
      //console.log("flagElement", flagElement ,"   normalement",str);
      if (flagElement && !flagElement.classList.contains("flag")) {
        flagElement.classList.add("flag");
        //console.log("added : flag")
      }
  
      // Only run the interval once (optional improvement)
      if (!window._flagIntervalSet) {
        setInterval(() => {
          const flags = document.querySelectorAll('.flag'); // FIXED SELECTOR
          console.log("flags:", flags.length);
  
          flags.forEach(label => {
            label.classList.toggle("BULGE");
            const chars = label.textContent.split('');
            label.innerHtml = ''; // Clear the label
  
            chars.forEach(char => {
              label.innerHtml += \`\${char === ' ' ? '&nbsp;' : char}\`;
              wait(100);
            });
          });
        }, 2000);
        window._flagIntervalSet = true; // prevent multiple intervals
      }
  
    } else {
      TicketIds.forEach(ticketId => {
        const peace = id.split('-')[1];
        if (ticketId !== peace) {
          const otherCommentArea = document.getElementById(\`comment-box-\${ticketId}\`);
          if (otherCommentArea) {
            otherCommentArea.classList.remove('show');
          }
        }
      });
      commentArea?.classList?.add('hidden');
    }
  }
  

  async function submitComment(event, ticketId) {
    event.preventDefault();
  
    const form = event.target;
    const commentText = form.comment.value.trim();
  
    if (!commentText) return; // Prevent empty comments
  
    // Get selected radio button value
    let resolueValue = null;
    const radioOui = document.getElementById(\`resolu-\${ticketId}-oui\`);
    const radioNon = document.getElementById(\`resolu-\${ticketId}-non\`);
  
    if (radioOui && radioOui.checked) {
      resolueValue = "oui";
    } else if (radioNon && radioNon.checked) {
      resolueValue = "non";
    }
  
    const payload = {
      comment: commentText
    };
  
    if (resolueValue !== null) {
      payload.resolue = resolueValue;
    }
  
    try {
      const res = await fetch(\`/api/tickets/\${ticketId}/comments\`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
  
      if (!res.ok) throw new Error("Failed to post comment");
  
      form.reset();
      loadTickets();
    } catch (err) {
      console.error("Error submitting comment:", err);
      alert("❌ Erreur lors de l'envoi du commentaire.");
    }
  }
  
  
/*
    async function submitComment(event, ticketId) {
      event.preventDefault();
      const form = event.target;
      const comment = form.comment.value;
      const resolu_oui = document.getElementById("resolu-"+ticketId+"-oui") || false;
      const resolu_non = document.getElementById("resolu-"+ticketId+"-non") || false;
      if(resolu_oui && resolu_non){
        if(resolu_non.checked == true) comment.add("resolue":"non")
        else comment.add("resolue":"oui")
      }
      await fetch(\`/api/tickets/\${ticketId}/comments\`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ comment })
      });
      form.reset();
      loadTickets();
    }
*/
    function MyTickets() {
        const Me = AllTickets.filter(ticket => ticket.reported_by === MyName);
        if (Me.length === 0) {
            alert("❗ Vous n'avez pas de tickets.");
            return;
        }
        loadTickets(Me);
    }
    
    loadTickets();

    async function wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
      }

      document.getElementById('printRepresentation').addEventListener('click', async function() {
        const { jsPDF } = window.jspdf;
        
        try {
            // Create landscape PDF with better metadata
            const doc = new jsPDF({
                orientation: 'landscape',
                unit: 'mm',
                format: 'a4',
                filters: ['ASCIIHexEncode']
            });
            
            // Set document properties
            doc.setProperties({
                title: \`Rapport Tickets - \${document.getElementById('Mois').value}\`,
                subject: 'Statistiques des tickets',
                author: 'Portail Technique',
                keywords: 'tickets, statistiques, rapport',
                creator: 'Portail Technique'
            });
    
            const isChartView = document.getElementById('chartContainer').style.display !== 'none';
            
            if (isChartView) {
                await printChartsToPDF(doc);
            } else {
                await printTableToPDF(doc);
            }
    
            // Add secure footer
            addPDFFooter(doc);
            
            // Save with timestamp in filename
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:T]/g, '-');
            doc.save(\`rapport_tickets_\${timestamp}.pdf\`);
            
        } catch (error) {
            console.error('Échec de génération PDF:', error);
            alert('❌ Erreur lors de la génération du PDF: ' + error.message);
        }
    });
    
    // Print charts to PDF (optimized for landscape)
    async function printChartsToPDF(doc) {
        const pageWidth = doc.internal.pageSize.getWidth();
        const margin = 15;
        
        // Common header function
        const addHeader = () => {
            doc.setFont('helvetica', 'bold');
            doc.setFontSize(20);
            doc.setTextColor(40, 40, 40);
            doc.text('Rapport des Statistiques des Tickets', pageWidth / 2, margin, { align: 'center' });
            
            doc.setFontSize(12);
            doc.setFont('helvetica', 'normal');
            doc.text(\`Période: \${formatFrenchDate(document.getElementById('Mois').value)}\`, 
                     pageWidth / 2, margin + 10, { align: 'center' });
        };
    
        // Convert charts to images
        const [statusChartImg, systemChartImg] = await Promise.all([
            getEnhancedChartImage('#statusChart'),
            getEnhancedChartImage('#systemChart')
        ]);
    
        // PAGE 1: Status Chart
        addHeader();
        
        // Chart dimensions (80% of page height)
        const chartHeight = doc.internal.pageSize.getHeight() * 0.8;
        const chartWidth = chartHeight * 1.2;
        const chartX = (pageWidth - chartWidth) / 2;
        const chartX1 = (pageWidth - chartHeight) / 2;
        
        // Add status chart
        doc.addImage(statusChartImg, 'JPEG', chartX1, margin + 20, chartHeight, chartHeight);
        
        // Add statistics
        const statsY = margin + 20 + chartHeight + 10;
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text('Statistiques des Tickets:', margin, statsY);
        
        const openCount = getChartDataValue('#statusChart', 'Ouverts');
        const closedCount = getChartDataValue('#statusChart', 'Fermés');
        
        doc.setFontSize(11);
        doc.setFont('helvetica', 'normal');
        doc.text(\`• Tickets ouverts: \${openCount}\`, margin, statsY + 8);
        doc.text(\`• Tickets fermés: \${closedCount}\`, margin*3, statsY + 8);
    
        // PAGE 2: System Chart
        doc.addPage();
        addHeader();
        
        // Add system chart
        doc.addImage(systemChartImg, 'JPEG', chartX, margin + 20, chartWidth, chartHeight);
        
        // Add metrics
        const avgTimeText = document.getElementById('avgTime').textContent.trim();
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text('Métriques:', margin, statsY);
        
        doc.setFontSize(11);
        doc.setFont('helvetica', 'normal');
        doc.text(\`• Temps moyen de résolution: \${avgTimeText}\`, margin, statsY + 8);
    
        // Add footer to all pages
        doc.setFontSize(8);
        doc.setTextColor(100);
        const pageCount = doc.internal.getNumberOfPages();
        for (let i = 1; i <= pageCount; i++) {
            doc.setPage(i);
            doc.text(\`Page \${i}/\${pageCount}\`, margin/2, doc.internal.pageSize.height - 5, { align: 'left' });

        }
    }
    
    // Print table to PDF (optimized for landscape)
    async function printTableToPDF(doc) {
        const pageWidth = doc.internal.pageSize.getWidth();
        const margin = 15;
    
        // Header
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(20);
        doc.text('Détails des Tickets', pageWidth / 2, margin, { align: 'center' });
        
        // Subheader
        doc.setFontSize(12);
        doc.setFont('helvetica', 'normal');
        doc.text(\`Période: \${formatFrenchDate(document.getElementById('Mois').value)}\`, 
                 pageWidth / 2, margin + 10, { align: 'center' });
    
        // Get table data with error handling
        const { headers, rows } = await getTableData();
    
        // Generate table with autoTable
        doc.autoTable({
            head: [headers],
            body: rows,
            startY: margin + 20,
            styles: {
                fontSize: 9,
                cellPadding: 3,
                overflow: 'linebreak',
                halign: 'center',
                valign: 'middle'
            },
            columnStyles: {
                0: { cellWidth: 15, halign: 'center' }, // ID
                4: { halign: 'left' }, // Status
                5: { cellWidth: 25 }, // Date
                6: { cellWidth: 30 } // Resolution time
            },
            margin: { top: margin + 20 },
            didDrawPage: function(data) {
                // Page number
                doc.setFontSize(8);
                doc.setTextColor(100);
                doc.text(
                    \`Page \${data.pageNumber} sur \${data.pageCount}\`,
                    margin/2,
                    doc.internal.pageSize.height - 5,
                    { align: 'left' }
                );
                
                // Watermark on first page
                if (data.pageNumber === 1) {
                    doc.setFontSize(60);
                    doc.setTextColor(240, 240, 240);
                    doc.text(
                        '',
                        pageWidth / 2,
                        doc.internal.pageSize.getHeight() / 2,
                        { align: 'center', angle: 45 }
                    );
                    doc.setTextColor(0, 0, 0);
                }
            },
            tableLineWidth: 0.1,
            tableLineColor: 200
        });
    }
    
    // Helper functions
    function addPDFFooter(doc) {
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        
        doc.setFontSize(8);
        doc.setTextColor(100);
        doc.text(
            \`Généré le \${new Date().toLocaleString('fr-FR')}\`,
            pageWidth - 10,
            pageHeight - 6,
            { align: 'right' }
        );
        
        // Add small confidential notice
        doc.setFontSize(8);
        doc.text(
            '- Usage interne uniquement -',
            pageWidth - 10,
            pageHeight - 3,
            { align: 'right' }
        );
    }
    
    async function getEnhancedChartImage(selector) {
        return new Promise((resolve) => {
            const canvas = document.querySelector(selector);
            
            // Temporarily enhance chart for printing
            const originalBackground = canvas.style.background;
            canvas.style.background = 'white';
            
            html2canvas(canvas, {
                scale: 2, // Higher quality
                logging: false,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#FFFFFF'
            }).then(renderedCanvas => {
                canvas.style.background = originalBackground; // Restore original
                resolve(renderedCanvas.toDataURL('image/jpeg', 0.9));
            });
        });
    }
    
    function getChartDataValue(chartSelector, label) {
        const chart = Chart.getChart(document.querySelector(chartSelector));
        if (!chart) return 'N/A';
        
        const dataset = chart.data.datasets[0];
        const index = chart.data.labels.indexOf(label);
        return index !== -1 ? dataset.data[index] : 'N/A';
    }
    
    async function getTableData() {
        try {
            const headers = Array.from(document.querySelectorAll('#ticketTable thead th'))
                .map(th => th.textContent.trim());
            
            const rows = Array.from(document.querySelectorAll('#ticketTable tbody tr'))
                .map(tr => {
                    return Array.from(tr.querySelectorAll('td')).map(td => {
                        return td?.textContent?.trim() || '';
                    });
                })
                .filter(row => row.length > 0);
            
            return { headers, rows };
        } catch (error) {
            console.error('Erreur traitement tableau:', error);
            return { headers: [], rows: [] };
        }
    }
    
    function formatFrenchDate(monthString) {
        if (!monthString) return '';
        const [year, month] = monthString.split('-');
        const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                           'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
        return \`\${monthNames[parseInt(month) - 1]} \${year}\`;
    }
  </script>
</body>
</html>


`;